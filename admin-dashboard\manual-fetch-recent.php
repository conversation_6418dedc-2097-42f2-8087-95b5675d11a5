<?php
/**
 * Manual fetch of recent emails that might have been missed
 */

require_once 'config/config.php';
require_once 'classes/ImapEmailFetcher.php';

// Check if IMAP extension is loaded
if (!extension_loaded('imap')) {
    die("ERROR: PHP IMAP extension is not installed.\n");
}

/**
 * Extract only the reply content, removing quoted original message
 */
function extractReplyContent($emailBody) {
    // Common patterns that indicate quoted content
    $quotedPatterns = [
        '/^On .* wrote:.*$/m',           // "On [date] [person] wrote:"
        '/^From:.*$/m',                  // Email headers in quoted content
        '/^Sent:.*$/m',                  // Outlook style headers
        '/^To:.*$/m',                    // To headers
        '/^Subject:.*$/m',               // Subject headers
        '/^Date:.*$/m',                  // Date headers
        '/^>.*$/m',                      // Lines starting with >
        '/^-----Original Message-----.*$/m', // Outlook original message
        '/^________________________________.*$/m', // Outlook separator
        '/^\s*From:.*\n.*\n.*\n.*$/m',   // Multi-line email headers
    ];

    $lines = explode("\n", $emailBody);
    $replyLines = [];
    $foundQuotedContent = false;

    foreach ($lines as $line) {
        $line = trim($line);

        // Check if this line indicates start of quoted content
        foreach ($quotedPatterns as $pattern) {
            if (preg_match($pattern, $line)) {
                $foundQuotedContent = true;
                break 2; // Break out of both loops
            }
        }

        // If we haven't found quoted content yet, this is part of the reply
        if (!$foundQuotedContent && !empty($line)) {
            $replyLines[] = $line;
        }
    }

    // Join the reply lines and clean up
    $replyContent = implode("\n", $replyLines);
    $replyContent = trim($replyContent);

    // If we couldn't extract a clean reply, return the first paragraph
    if (empty($replyContent) || strlen($replyContent) < 10) {
        $paragraphs = explode("\n\n", $emailBody);
        $replyContent = trim($paragraphs[0]);
    }

    return $replyContent;
}

echo "=== Manual Fetch of Recent Emails ===\n";

// Connect to booking account
$connectionString = "{mail.melevatours.co.ke:993/imap/ssl}INBOX";
$email = '<EMAIL>';
$password = '1V^bAvDR!%)6,C&A';

$connection = imap_open($connectionString, $email, $password);

if (!$connection) {
    die("Failed to connect to IMAP server\n");
}

echo "✅ <NAME_EMAIL>\n";

// Get the last 5 emails (regardless of read status)
$totalEmails = imap_num_msg($connection);
echo "Total emails in mailbox: $totalEmails\n";

$startEmail = max(1, $totalEmails - 4); // Last 5 emails
echo "Checking emails #$startEmail to #$totalEmails\n\n";

$db = Database::getInstance()->getConnection();
$processedCount = 0;

for ($i = $totalEmails; $i >= $startEmail; $i--) {
    echo "--- Processing Email #$i ---\n";
    
    $header = imap_headerinfo($connection, $i);
    if (!$header) {
        echo "❌ Could not get header\n\n";
        continue;
    }
    
    // Get sender info
    $from = $header->from[0] ?? null;
    if (!$from) {
        echo "❌ No sender info\n\n";
        continue;
    }
    
    $senderEmail = strtolower($from->mailbox . '@' . $from->host);
    $senderName = isset($from->personal) ? imap_mime_header_decode($from->personal)[0]->text : $senderEmail;
    
    // Get subject
    $subject = isset($header->subject) ? imap_mime_header_decode($header->subject)[0]->text : 'No Subject';
    
    // Get date
    $receivedAt = date('Y-m-d H:i:s', $header->udate);
    
    echo "From: $senderName <$senderEmail>\n";
    echo "Subject: $subject\n";
    echo "Date: $receivedAt\n";
    
    // Check if this email already exists in database
    $stmt = $db->prepare("
        SELECT message_id FROM messages 
        WHERE sender_email = ? AND subject = ? AND received_at = ?
    ");
    $stmt->execute([$senderEmail, $subject, $receivedAt]);
    $existingMessage = $stmt->fetch();
    
    if ($existingMessage) {
        echo "⚠️  Already exists in database (ID: {$existingMessage['message_id']})\n\n";
        continue;
    }
    
    // Skip if it's from the system itself
    if (strpos($senderEmail, 'melevatours.co.ke') !== false) {
        echo "⏭️  Skipping system email\n\n";
        continue;
    }
    
    echo "✅ New customer email - processing...\n";
    
    // Get email body
    $body = imap_body($connection, $i);
    
    // Decode if needed
    $structure = imap_fetchstructure($connection, $i);
    if ($structure->encoding == 3) { // Base64
        $body = base64_decode($body);
    } elseif ($structure->encoding == 4) { // Quoted-printable
        $body = quoted_printable_decode($body);
    }
    
    // Clean up the body
    $body = strip_tags($body);
    $body = trim($body);

    // Extract only the reply content (remove quoted original message)
    $cleanedBody = extractReplyContent($body);

    // Determine message category (booking emails are quote-related, others are contact)
    $messageCategory = (strpos($email, 'booking@') !== false) ? 'quote' : 'contact';

    // Insert into database
    try {
        $stmt = $db->prepare("
            INSERT INTO messages (
                sender_name, sender_email, subject, message_content,
                received_at, message_type, message_category, is_read, email_message_id
            ) VALUES (?, ?, ?, ?, ?, 'incoming', ?, 0, ?)
        ");

        $messageId = $header->message_id ?? 'manual_' . time() . '_' . $i;

        $stmt->execute([
            $senderName,
            $senderEmail,
            $subject,
            $cleanedBody,
            $receivedAt,
            $messageCategory,
            $messageId
        ]);
        
        $newMessageId = $db->lastInsertId();
        echo "✅ Saved to database with ID: $newMessageId\n";
        $processedCount++;
        
    } catch (Exception $e) {
        echo "❌ Database error: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

imap_close($connection);

echo "=== Summary ===\n";
echo "Processed $processedCount new emails\n";

// Check current unread count
$stmt = $db->query("SELECT COUNT(*) as count FROM messages WHERE is_read = 0");
$unread = $stmt->fetch();
echo "Total unread messages now: {$unread['count']}\n";

echo "\n=== Manual Fetch Complete ===\n";
?>
