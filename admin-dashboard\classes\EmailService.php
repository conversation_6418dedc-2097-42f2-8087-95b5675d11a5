<?php
/**
 * Email Service Class
 * Handles all email functionality for the application with different sender addresses
 */

// Include PHPMailer classes
use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

class EmailService {
    private $mail;
    private $contactModel;

    // Email configuration for cPanel SMTP (SSL Settings)
    private $emailConfig = [
        'smtp_host' => 'melevatours.co.ke', // SSL: melevatours.co.ke | Non-SSL: mail.melevatours.co.ke
        'smtp_port' => 465, // SSL: 465 | Non-SSL: 26
        'smtp_username' => '<EMAIL>',
        'smtp_password' => 'hi$Ch9=lYcap{7cA',

        // Different sender addresses for different purposes
        'admin_notifications' => [
            'from_email' => '<EMAIL>',
            'from_name' => 'Meleva Tours and Travel Admin System'
        ],
        'user_confirmations' => [
            'from_email' => '<EMAIL>',
            'from_name' => 'Meleva Tours and Travel'
        ],
        'quote_requests' => [
            'from_email' => '<EMAIL>',
            'from_name' => 'Meleva Tours and Travel - Booking Department'
        ],
        'contact_messages' => [
            'from_email' => '<EMAIL>',
            'from_name' => 'Meleva Tours and Travel - Customer Service'
        ],

        // Use emoji-free templates to avoid encoding issues
        'use_emoji_free' => true
    ];

    public function __construct() {
        // Load Composer's autoloader
        require_once __DIR__ . '/../vendor/autoload.php';

        // Include models if not already included
        if (!class_exists('ContactInfo')) {
            require_once __DIR__ . '/models.php';
        }

        $this->mail = new PHPMailer(true);
        $this->contactModel = new ContactInfo();
        $this->setupSMTP();
    }

    /**
     * Get logo as base64 for reliable email display
     */
    private function getLogoBase64() {
        // Smart path detection for logo based on host
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

        if (strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false) {
            // Localhost: Try multiple possible paths
            $possiblePaths = [
                $_SERVER['DOCUMENT_ROOT'] . '/meleva/images/meleva-lg.png',  // Standard localhost path
                __DIR__ . '/../../images/meleva-lg.png',                    // Relative from classes folder
                dirname(dirname(dirname(__FILE__))) . '/images/meleva-lg.png' // Go up from admin-dashboard/classes
            ];
        } else {
            // Live server: /images/meleva-lg.png (root/images/)
            $possiblePaths = [
                $_SERVER['DOCUMENT_ROOT'] . '/images/meleva-lg.png'
            ];
        }

        // Try each path until we find the logo
        foreach ($possiblePaths as $logoPath) {
            error_log("Trying logo path: " . $logoPath);

            if (file_exists($logoPath) && is_readable($logoPath)) {
                $imageData = file_get_contents($logoPath);
                if ($imageData !== false && strlen($imageData) > 0) {
                    // Check file size - some email clients have limits
                    if (strlen($imageData) > 500000) { // 500KB limit
                        error_log("Logo file too large for email: " . strlen($imageData) . " bytes from: " . $logoPath);
                        continue;
                    } else {
                        $base64 = base64_encode($imageData);
                        error_log("Logo loaded successfully from: " . $logoPath);
                        return 'data:image/png;base64,' . $base64;
                    }
                }
            } else {
                error_log("Logo not found or not readable at: " . $logoPath);
            }
        }

        // Use text logo as fallback for all environments
        error_log("Using text logo fallback - no image logo found");
        return $this->createTextLogo();
    }

    /**
     * Get the correct base URL for the website
     */
    public function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

        // Check if we're on localhost (development) or live server
        if (strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false) {
            // Localhost - include /meleva path
            return $protocol . '://' . $host . '/meleva';
        } else {
            // Live server - no /meleva path needed
            return $protocol . '://' . $host;
        }
    }

    /**
     * Get common email styles including logo styling
     */
    private function getCommonEmailStyles() {
        return "
            .logo {
                margin-bottom: 15px;
                text-align: center;
            }
            .logo img {
                display: block;
                margin: 0 auto;
                max-width: 100px;
                height: auto;
                border-radius: 8px;
            }
            .footer {
                text-align: center;
                margin-top: 25px;
                color: #64748b;
                font-size: 12px;
            }
        ";
    }

    /**
     * Create a simple text-based logo fallback
     */
    private function createTextLogo() {
        // Create a professional SVG logo as fallback
        $svg = '<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#f97316;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#ea580c;stop-opacity:1" />
                </linearGradient>
            </defs>
            <rect width="100" height="100" rx="12" fill="url(#grad1)"/>
            <text x="50" y="30" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="white">MELEVA</text>
            <text x="50" y="48" font-family="Arial, sans-serif" font-size="8" text-anchor="middle" fill="white">TOURS &amp; TRAVEL</text>
            <circle cx="50" cy="65" r="12" fill="none" stroke="white" stroke-width="2"/>
            <text x="50" y="70" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white">SAFARI</text>
            <text x="50" y="88" font-family="Arial, sans-serif" font-size="6" text-anchor="middle" fill="white">ADVENTURES</text>
        </svg>';

        error_log("Using fallback text logo for email");
        return 'data:image/svg+xml;base64,' . base64_encode($svg);
    }

    private function setupSMTP() {
        try {
            // Server settings for cPanel SMTP
            $this->mail->isSMTP();
            $this->mail->Host = $this->emailConfig['smtp_host'];
            $this->mail->SMTPAuth = true;
            $this->mail->Username = $this->emailConfig['smtp_username'];
            $this->mail->Password = $this->emailConfig['smtp_password'];

            // Use STARTTLS for port 587, or SSL for port 465
            if ($this->emailConfig['smtp_port'] == 465) {
                $this->mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
            } else {
                $this->mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            }

            // SMTP debugging (set to 0 for production, 2 for troubleshooting)
            $this->mail->SMTPDebug = 0; // Disabled for production

            // Add error logging for debugging
            error_log("SMTP Configuration: Host={$this->emailConfig['smtp_host']}, Port={$this->emailConfig['smtp_port']}, Username={$this->emailConfig['smtp_username']}");
            $this->mail->Debugoutput = 'error_log';

            $this->mail->Port = $this->emailConfig['smtp_port'];

            // Additional settings for cPanel compatibility and spam prevention
            $this->mail->SMTPOptions = array(
                'ssl' => array(
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                )
            );

            // Character encoding and anti-spam settings
            $this->mail->CharSet = 'UTF-8';
            $this->mail->Encoding = 'base64';
            $this->mail->XMailer = 'Meleva Tours Customer Service v1.0';
            $this->mail->Priority = 3; // Normal priority
        } catch (Exception $e) {
            error_log("SMTP Setup Error: " . $e->getMessage());
        }
    }
    
    /**
     * Send admin notification email when new message is received
     */
    public function sendAdminNotification($messageData, $messageType = 'contact') {
        try {
            // Determine admin email and sender based on message type
            if ($messageType === 'quote') {
                // Quote requests go to booking department
                $adminEmail = '<EMAIL>';
                $senderConfig = $this->emailConfig['quote_requests'];
            } elseif ($messageType === 'contact') {
                // Contact messages go to info email
                $adminEmail = '<EMAIL>';
                $senderConfig = $this->emailConfig['contact_messages'];
            } else {
                // Other messages go to general admin email
                $contactInfo = $this->contactModel->getCurrent();
                $adminEmail = $contactInfo['email'] ?? '<EMAIL>';
                $senderConfig = $this->emailConfig['admin_notifications'];
            }

            // Clear any previous recipients and set sender
            $this->mail->clearAddresses();
            $this->mail->clearReplyTos();

            // Set sender based on message type
            $this->mail->setFrom(
                $senderConfig['from_email'],
                $senderConfig['from_name']
            );

            // Set reply-to as the original sender to maintain conversation
            $this->mail->addReplyTo($messageData['sender_email'], $messageData['sender_name']);

            $this->mail->addAddress($adminEmail);

            // Email content
            $this->mail->isHTML(true);
            $this->mail->Subject = 'New ' . ucfirst($messageType) . ' Message - Meleva Tours';

            $this->mail->Body = $this->getAdminNotificationTemplate($messageData, $messageType);
            $this->mail->AltBody = $this->getAdminNotificationPlainText($messageData, $messageType);

            return $this->mail->send();
        } catch (Exception $e) {
            error_log("Admin notification email error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send confirmation email to user
     */
    public function sendUserConfirmation($userData, $messageType = 'contact') {
        try {
            // Clear any previous recipients and set sender
            $this->mail->clearAddresses();
            $this->mail->clearReplyTos();

            // Set appropriate sender based on message type
            if ($messageType === 'quote') {
                $this->mail->setFrom(
                    $this->emailConfig['user_confirmations']['from_email'],
                    $this->emailConfig['user_confirmations']['from_name']
                );
                // Set reply-to for quote confirmations to booking department
                $this->mail->addReplyTo('<EMAIL>', 'Meleva Tours and Travel - Booking Department');
            } else {
                $this->mail->setFrom(
                    $this->emailConfig['user_confirmations']['from_email'],
                    $this->emailConfig['user_confirmations']['from_name']
                );
                // Set reply-to for contact confirmations to info email
                $this->mail->addReplyTo('<EMAIL>', 'Meleva Tours and Travel - Customer Service');
            }

            $this->mail->addAddress($userData['email'], $userData['name']);

            // Email content
            $this->mail->isHTML(true);
            $subject = $messageType === 'quote'
                ? 'Your Travel Quote Request - Meleva Tours'
                : 'Thank you for contacting Meleva Tours';
            $this->mail->Subject = $subject;

            $this->mail->Body = $this->getUserConfirmationTemplate($userData, $messageType);
            $this->mail->AltBody = $this->getUserConfirmationPlainText($userData, $messageType);

            $result = $this->mail->send();
            error_log("User confirmation email sent successfully to: " . $userData['email']);
            return $result;
        } catch (Exception $e) {
            error_log("User confirmation email error: " . $e->getMessage());
            error_log("SMTP Error Info: " . $this->mail->ErrorInfo);
            return false;
        }
    }

    /**
     * Send quote request confirmation email to customer (simplified system)
     */
    public function sendQuoteRequestConfirmation($quoteData) {
        try {
            // Clear any previous recipients and set sender
            $this->mail->clearAddresses();
            $this->mail->clearReplyTos();

            $this->mail->setFrom(
                $this->emailConfig['user_confirmations']['from_email'],
                $this->emailConfig['user_confirmations']['from_name']
            );

            // Set reply-to for quote confirmations to booking department
            $contactInfo = $this->contactModel->getCurrent();
            $bookingEmail = $contactInfo['booking_email'] ?? '<EMAIL>';
            $this->mail->addReplyTo($bookingEmail, 'Meleva Tours and Travel - Booking Department');

            $this->mail->addAddress($quoteData['customer_email'], $quoteData['customer_name']);

            // Email content
            $this->mail->isHTML(true);
            $this->mail->Subject = 'Quote Request Received - Meleva Tours & Travel';

            // Convert quote data to user data format for template, preserving quote details
            $userData = [
                'name' => $quoteData['customer_name'],
                'email' => $quoteData['customer_email'],
                'quote_reference' => $quoteData['quote_reference'],
                'travel_date' => $quoteData['travel_date'],
                'number_of_adults' => $quoteData['number_of_adults'],
                'number_of_children' => $quoteData['number_of_children'],
                'special_requirements' => $quoteData['special_requirements']
            ];
            $this->mail->Body = $this->getUserConfirmationTemplate($userData, 'quote');
            $this->mail->AltBody = $this->getUserConfirmationPlainText($userData, 'quote');

            $result = $this->mail->send();
            error_log("Quote request confirmation email sent successfully to: " . $quoteData['customer_email']);
            return $result;
        } catch (Exception $e) {
            error_log("Quote request confirmation email error: " . $e->getMessage());
            error_log("SMTP Error Info: " . $this->mail->ErrorInfo);
            return false;
        }
    }

    /**
     * Send quote request notification email to admin (simplified system)
     */
    public function sendQuoteRequestNotification($quoteData) {
        try {
            // Quote requests go to booking department
            $contactInfo = $this->contactModel->getCurrent();
            $adminEmail = $contactInfo['booking_email'] ?? '<EMAIL>';

            // Clear any previous recipients and set sender
            $this->mail->clearAddresses();
            $this->mail->clearReplyTos();

            $this->mail->setFrom(
                $this->emailConfig['quote_requests']['from_email'],
                $this->emailConfig['quote_requests']['from_name']
            );

            // Set reply-to as the customer to maintain conversation
            $this->mail->addReplyTo($quoteData['customer_email'], $quoteData['customer_name']);

            $this->mail->addAddress($adminEmail);

            // Email content
            $this->mail->isHTML(true);
            $this->mail->Subject = '[NEW QUOTE REQUEST] ' . $quoteData['quote_reference'] . ' - ' . $quoteData['customer_name'];

            $this->mail->Body = $this->getQuoteRequestNotificationTemplate($quoteData);
            $this->mail->AltBody = $this->getQuoteRequestNotificationPlainText($quoteData);

            return $this->mail->send();
        } catch (Exception $e) {
            error_log("Quote request notification email error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send quote change notification to customer
     */
    public function sendQuoteChangeNotification($quoteData, $changeData, $adminUser = null) {
        try {
            // Clear any previous recipients and set sender
            $this->mail->clearAddresses();
            $this->mail->clearReplyTos();

            // Use main SMTP configuration (already set in setupSMTP)

            // Send directly from booking email
            $contactInfo = $this->contactModel->getCurrent();
            $bookingEmail = $contactInfo['booking_email'] ?? '<EMAIL>';

            $this->mail->setFrom(
                $bookingEmail,
                'Meleva Tours and Travel - Booking Department'
            );

            // Set reply-to to the same booking email for consistency
            $this->mail->addReplyTo($bookingEmail, 'Meleva Tours & Travel - Booking Department');

            $this->mail->addAddress($quoteData['customer_email'], $quoteData['customer_name']);

            // Email content
            $this->mail->isHTML(true);
            $this->mail->Subject = 'Quote Updated - ' . $quoteData['quote_reference'] . ' | Meleva Tours';

            // Add conversation tracking headers
            $conversationId = 'QUOTE_' . $quoteData['quote_reference'];
            $messageId = $this->generateEmailMessageId();
            // Don't add Message-ID header - PHPMailer generates this automatically
            $this->mail->addCustomHeader('X-Conversation-ID', $conversationId);
            $this->mail->addCustomHeader('X-Quote-Reference', $quoteData['quote_reference']);

            $this->mail->Body = $this->getQuoteChangeEmailTemplate($quoteData, $changeData, $adminUser);
            $this->mail->AltBody = $this->getQuoteChangeEmailPlainText($quoteData, $changeData, $adminUser);

            $result = $this->mail->send();

            // Track the email in database
            if ($result) {
                $this->trackQuoteEmail($quoteData, $messageId, $conversationId, 'quote_updated');
                error_log("Quote change email sent successfully to: " . $quoteData['customer_email']);
            } else {
                error_log("Quote change email failed to send to: " . $quoteData['customer_email']);
                error_log("PHPMailer Error: " . $this->mail->ErrorInfo);
            }

            return $result;
        } catch (Exception $e) {
            error_log("Quote change email exception: " . $e->getMessage());
            error_log("SMTP Error Info: " . $this->mail->ErrorInfo);
            error_log("Stack trace: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Send quote to customer with payment link (simplified system)
     */
    public function sendQuoteToCustomer($quoteData, $adminUser = null) {
        try {
            // Clear any previous recipients and set sender
            $this->mail->clearAddresses();
            $this->mail->clearReplyTos();

            // Send directly from booking email (now that duplicate Message-ID issue is fixed)
            $contactInfo = $this->contactModel->getCurrent();
            $bookingEmail = $contactInfo['booking_email'] ?? '<EMAIL>';

            $this->mail->setFrom(
                $bookingEmail,
                'Meleva Tours and Travel - Booking Department'
            );

            // Set reply-to to the same booking email for consistency
            $this->mail->addReplyTo($bookingEmail, 'Meleva Tours & Travel - Booking Department');

            $this->mail->addAddress($quoteData['customer_email'], $quoteData['customer_name']);

            // Email content
            $this->mail->isHTML(true);
            $this->mail->Subject = 'Your Travel Quote is Ready - ' . $quoteData['quote_reference'] . ' | Meleva Tours';

            // Add conversation tracking headers
            $conversationId = 'QUOTE_' . $quoteData['quote_reference'];
            $messageId = $this->generateEmailMessageId();
            // Don't add Message-ID header - PHPMailer generates this automatically
            // Adding custom Message-ID causes "multiple Message-ID headers" error in Gmail
            $this->mail->addCustomHeader('X-Conversation-ID', $conversationId);
            $this->mail->addCustomHeader('X-Quote-Reference', $quoteData['quote_reference']);

            $this->mail->Body = $this->getQuoteEmailTemplate($quoteData, $adminUser);
            $this->mail->AltBody = $this->getQuoteEmailPlainText($quoteData, $adminUser);

            $result = $this->mail->send();

            if ($result) {
                error_log("Quote email sent successfully to: " . $quoteData['customer_email']);

                // Track the email in database (don't let tracking failure affect email result)
                try {
                    $this->trackQuoteEmail($quoteData, $messageId, $conversationId, 'quote_sent');
                } catch (Exception $trackingError) {
                    error_log("Quote email tracking failed (but email was sent): " . $trackingError->getMessage());
                }
            } else {
                error_log("Quote email failed to send to: " . $quoteData['customer_email']);
                error_log("PHPMailer Error: " . $this->mail->ErrorInfo);
            }

            return $result;
        } catch (Exception $e) {
            error_log("Quote email error: " . $e->getMessage());
            error_log("SMTP Error Info: " . $this->mail->ErrorInfo);
            return false;
        }
    }
    
    /**
     * Get admin notification HTML template
     */
    private function getAdminNotificationTemplate($data, $type) {
        $messageTypeTitle = $type === 'quote' ? 'Quote Request' : 'Contact Message';
        
        return "
        <html>
        <head>
            <title>New {$messageTypeTitle} - Meleva Tours and Travel</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #f97316, #ea580c); color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
                .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
                .field { margin-bottom: 15px; }
                .field-label { font-weight: bold; color: #f97316; }
                .field-value { margin-top: 5px; padding: 10px; background: white; border-radius: 4px; border-left: 4px solid #f97316; }
                .btn { display: inline-block; background: #f97316; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; text-decoration: none; }
                " . $this->getCommonEmailStyles() . "
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <div class='logo'>
                        <img src='" . $this->getLogoBase64() . "' alt='Meleva Tours and Travel'>
                    </div>
                    <h2>{$messageTypeTitle}</h2>
                </div>
                <div class='content'>
                    <div class='field'>
                        <div class='field-label'>From:</div>
                        <div class='field-value'>{$data['sender_name']} ({$data['sender_email']})</div>
                    </div>
                    <div class='field'>
                        <div class='field-label'>Subject:</div>
                        <div class='field-value'>{$data['subject']}</div>
                    </div>
                    <div class='field'>
                        <div class='field-label'>Message:</div>
                        <div class='field-value'>" . nl2br(htmlspecialchars($data['message_content'])) . "</div>
                    </div>
                    <div class='field'>
                        <div class='field-label'>Received:</div>
                        <div class='field-value'>" . date('F j, Y \a\t g:i A') . "</div>
                    </div>
                    
                    <p style='text-align: center; margin-top: 30px;'>
                        <a href='" . $this->getBaseUrl() . "/admin-dashboard/messages.php' class='btn'>
                            View in Admin Dashboard
                        </a>
                    </p>
                </div>
                <div class='footer'>
                    <p>This email was automatically generated by your Meleva Tours website.</p>
                </div>
            </div>
        </body>
        </html>";
    }
    
    /**
     * Get admin notification plain text version
     */
    private function getAdminNotificationPlainText($data, $type) {
        $messageTypeTitle = $type === 'quote' ? 'Quote Request' : 'Contact Message';
        $priority = $type === 'quote' ? '[HIGH PRIORITY] ' : '';

        return "{$priority}New {$messageTypeTitle} - Meleva Tours and Travel\n\n" .
               "From: {$data['sender_name']} ({$data['sender_email']})\n" .
               "Subject: {$data['subject']}\n" .
               "Received: " . date('F j, Y \a\t g:i A T') . "\n\n" .
               "Message:\n" . str_repeat('-', 50) . "\n" .
               $data['message_content'] . "\n" .
               str_repeat('-', 50) . "\n\n" .
               "QUICK REPLY: You can reply directly to this email\n" .
               "ADMIN DASHBOARD: " . $this->getBaseUrl() . "/admin-dashboard/messages.php\n" .
               "ADMIN DASHBOARD: " . $this->getBaseUrl() . "/admin-dashboard/messages.php";
    }

    /**
     * Get user confirmation HTML template
     */
    private function getUserConfirmationTemplate($data, $type) {
        $contactInfo = $this->contactModel->getCurrent();
        $messageTypeText = $type === 'quote' ? 'travel quote request' : 'message';
        // Removed response time duration as requested
        $primaryColor = $type === 'quote' ? '#dc2626' : '#f97316';

        return "
        <html>
        <head>
            <title>Thank you for contacting Meleva Tours and Travel</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; background: #ffffff; }
                .header { background: linear-gradient(135deg, {$primaryColor}, #ea580c); color: white; padding: 25px; text-align: center; border-radius: 8px 8px 0 0; }
                .content { background: #f8fafc; padding: 25px; border-radius: 0 0 8px 8px; border: 1px solid #e2e8f0; }
                .highlight { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid {$primaryColor}; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .contact-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e2e8f0; }
                .contact-item { display: flex; align-items: center; margin-bottom: 10px; }
                .contact-icon { width: 20px; margin-right: 10px; color: {$primaryColor}; }
                .thank-you { font-size: 18px; color: {$primaryColor}; font-weight: bold; margin: 15px 0; }
                " . $this->getCommonEmailStyles() . "
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <div class='logo'>
                        <img src='" . $this->getLogoBase64() . "' alt='Meleva Tours and Travel'>
                    </div>
                    <h2>" . ucfirst($messageTypeText) . "</h2>
                </div>
                <div class='content'>
                    <p>Dear <strong>{$data['name']}</strong>,</p>

                    " . ($type === 'quote'
                        ? "<p>Thank you for your interest in Meleva Tours and Travel! We have successfully received your travel quote request and our experienced safari specialists are already working on creating a personalized proposal for you.</p>

                    <div class='highlight'>
                        <strong>Quote Reference:</strong> " . (isset($data['quote_reference']) ? $data['quote_reference'] : 'N/A') . "<br>
                        <strong>Status:</strong> Under Review
                    </div>

                    <div class='highlight'>
                        <h3 style='color: {$primaryColor}; margin-top: 0;'>Your Request Details</h3>
                        <p><strong>Travel Date:</strong> " . (isset($data['travel_date']) && $data['travel_date'] ? date('F j, Y', strtotime($data['travel_date'])) : 'To be determined') . "</p>
                        <p><strong>Travelers:</strong> " . (isset($data['number_of_adults']) ? $data['number_of_adults'] : '0') . " Adult(s)" . (isset($data['number_of_children']) && $data['number_of_children'] > 0 ? ", " . $data['number_of_children'] . " Child(ren)" : "") . "</p>
                        " . (isset($data['special_requirements']) && !empty($data['special_requirements']) ? "<p><strong>Special Requirements:</strong> " . htmlspecialchars($data['special_requirements']) . "</p>" : "") . "
                    </div>

                    <p><strong>What happens next?</strong></p>
                    <ul>
                        <li>Our team will review your requirements and preferences</li>
                        <li>We'll prepare a detailed quote with itinerary and pricing</li>
                        <li>You'll receive your personalized quote via email with a payment link</li>
                        <li>Once you're ready, you can proceed with payment using the secure link</li>
                    </ul>"
                        : "<p>Thank you for contacting Meleva Tours and Travel! We have successfully received your {$messageTypeText} and our experienced team will review it shortly.</p>

                    <div class='highlight'>
                        <strong>Reference:</strong> " . strtoupper(substr(md5($data['email'] . time()), 0, 8)) . "<br>
                        <strong>Status:</strong> Under review
                    </div>

                    <p>In the meantime, feel free to explore our website or contact us directly if you have any urgent questions.</p>") . "

                    <div class='contact-info'>
                        <h3 style='color: {$primaryColor}; margin-top: 0;'> Need Immediate Assistance?</h3>
                        <div class='contact-item'>
                            <span class='contact-icon'></span>
                            <span><strong>Phone:</strong> {$contactInfo['phone_number']}</span>
                        </div>
                        <div class='contact-item'>
                            <span class='contact-icon'></span>
                            <span><strong>Email:</strong> " . ($type === 'quote' ? ($contactInfo['booking_email'] ?? '<EMAIL>') : $contactInfo['email']) . "</span>
                        </div>
                        <div class='contact-item'>
                            <span class='contact-icon'></span>
                            <span><strong>Location:</strong> {$contactInfo['address']}</span>
                        </div>
                        <div class='contact-item'>
                            <span class='contact-icon'></span>
                            <span><strong>Hours:</strong> {$contactInfo['working_hours']}</span>
                        </div>
                    </div>

                    <p>We look forward to helping you create unforgettable safari memories!</p>

                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='" . $this->getBaseUrl() . "/' class='btn'>Visit Our Website</a>
                        <a href='" . $this->getBaseUrl() . "/tours.php' class='btn'>View Tours</a>
                        <a href='" . $this->getBaseUrl() . "/contact.php' class='btn'>Contact Us</a>
                    </div>

                    <p style='margin-top: 30px;'>
                        <strong>Best regards,</strong><br>
                        Meleva Tours and Travel<br>
                        <em><small>Smooth Travels, Seamless Experiences!</small></em>
                    </p>
                </div>
                <div class='footer'>
                    <p> This is an automated confirmation email. Please do not reply to this message.</p>
                    <p>For immediate assistance, please use the contact information above.</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Get user confirmation plain text version
     */
    private function getUserConfirmationPlainText($data, $type) {
        $contactInfo = $this->contactModel->getCurrent();
        $messageTypeText = $type === 'quote' ? 'travel quote request' : 'message';
        // Removed response time duration as requested
        $reference = strtoupper(substr(md5($data['email'] . time()), 0, 8));

        return "MELEVA TOURS AND TRAVEL - " . ($type === 'quote' ? 'Quote Request Received' : 'Thank you for contacting us!') . "\n\n" .
               "Dear {$data['name']},\n\n" .
               ($type === 'quote'
                   ? "Thank you for your interest in Meleva Tours and Travel! We have successfully received your travel quote request and our experienced safari specialists are already working on creating a personalized proposal for you.\n\n" .
                     "QUOTE DETAILS:\n" .
                     "Quote Reference: " . (isset($data['quote_reference']) ? $data['quote_reference'] : 'N/A') . "\n" .
                     "Status: Under Review\n\n" .
                     "YOUR REQUEST DETAILS:\n" .
                     "Travel Date: " . (isset($data['travel_date']) && $data['travel_date'] ? date('F j, Y', strtotime($data['travel_date'])) : 'To be determined') . "\n" .
                     "Travelers: " . (isset($data['number_of_adults']) ? $data['number_of_adults'] : '0') . " Adult(s)" . (isset($data['number_of_children']) && $data['number_of_children'] > 0 ? ", " . $data['number_of_children'] . " Child(ren)" : "") . "\n" .
                     (isset($data['special_requirements']) && !empty($data['special_requirements']) ? "Special Requirements: " . $data['special_requirements'] . "\n" : "") . "\n" .
                     "WHAT HAPPENS NEXT:\n" .
                     "- Our team will review your requirements and preferences\n" .
                     "- We'll prepare a detailed quote with itinerary and pricing\n" .
                     "- You'll receive your personalized quote via email with a payment link\n" .
                     "- Once you're ready, you can proceed with payment using the secure link\n\n"
                   : "MESSAGE SUCCESSFULLY RECEIVED\n\n" .
                     "We have successfully received your {$messageTypeText} and our experienced team will review it shortly.\n\n" .
                     "RESPONSE DETAILS:\n" .
                     "Reference Number: {$reference}\n" .
                     "Status: Under review\n\n" .
                     "In the meantime, feel free to explore our website or contact us directly if you have any urgent questions.\n\n") .
               "NEED IMMEDIATE ASSISTANCE?\n" .
               "Phone: {$contactInfo['phone_number']}\n" .
               "Email: " . ($type === 'quote' ? ($contactInfo['booking_email'] ?? '<EMAIL>') : $contactInfo['email']) . "\n" .
               "Location: {$contactInfo['address']}\n" .
               "Hours: {$contactInfo['working_hours']}\n\n" .
               "QUICK LINKS:\n" .
               "Website: " . $this->getBaseUrl() . "/\n" .
               "View Tours: " . $this->getBaseUrl() . "/tours.php\n" .
               "Contact Us: " . $this->getBaseUrl() . "/contact.php\n\n" .
               "We look forward to helping you create unforgettable safari memories!\n\n" .
               "Best regards,\n" .
               "Meleva Tours and Travel\n" .
               "Smooth Travels, Seamless Experiences!\n\n" .
               "This is an automated confirmation email. Please do not reply to this message.\n" .
               "For immediate assistance, please use the contact information above.";
    }

    /**
     * Send reply email to user when admin responds to their message
     */
    public function sendReplyEmail($originalMessage, $replyContent, $adminUser) {
        try {
            // Clear any previous recipients and set sender
            $this->mail->clearAddresses();
            $this->mail->clearReplyTos();

            // Determine message type to set appropriate reply-to
            $messageType = strpos(strtolower($originalMessage['subject']), 'quote') !== false ? 'quote' : 'contact';

            if ($messageType === 'quote') {
                // Quote-related replies - update SMTP credentials to match sender
                $this->mail->Username = '<EMAIL>';
                $this->mail->Password = 'hi$Ch9=lYcap{7cA';
                $this->mail->setFrom('<EMAIL>', 'Meleva Tours - Booking Department');
                $this->mail->addReplyTo('<EMAIL>', 'Meleva Tours - Booking Department');
            } else {
                // Contact-related replies - use default info credentials
                $this->mail->Username = '<EMAIL>';
                $this->mail->Password = 'hi$Ch9=lYcap{7cA';
                $this->mail->setFrom('<EMAIL>', 'Meleva Tours - Customer Service');
                $this->mail->addReplyTo('<EMAIL>', 'Meleva Tours - Customer Service');
            }

            $this->mail->addAddress($originalMessage['sender_email'], $originalMessage['sender_name']);

            // Determine message type for styling
            $messageType = strpos(strtolower($originalMessage['subject']), 'quote') !== false ? 'quote' : 'contact';

            // Generate conversation tracking headers
            $conversationId = $this->generateConversationId($originalMessage);
            $messageId = $this->generateEmailMessageId();

            // Set email headers for conversation threading and spam prevention
            // Don't add Message-ID header - PHPMailer generates this automatically
            $this->mail->addCustomHeader('X-Conversation-ID', $conversationId);

            // Anti-spam headers
            $this->mail->addCustomHeader('X-Mailer', 'Meleva Tours Customer Service System');
            $this->mail->addCustomHeader('X-Priority', '3');
            $this->mail->addCustomHeader('X-MSMail-Priority', 'Normal');
            $this->mail->addCustomHeader('Importance', 'Normal');
            $this->mail->addCustomHeader('X-Auto-Response-Suppress', 'OOF, DR, RN, NRN, AutoReply');

            // Business legitimacy headers
            $this->mail->addCustomHeader('Organization', 'Meleva Tours and Travel');
            $this->mail->addCustomHeader('X-Business-Type', 'Travel and Tourism');
            $this->mail->addCustomHeader('List-Unsubscribe', '<mailto:<EMAIL>>');

            // Content classification
            $this->mail->addCustomHeader('X-Content-Type', 'Customer Service Reply');
            $this->mail->addCustomHeader('X-Message-Category', 'Transactional');

            // If this is a reply, set In-Reply-To header
            if (isset($originalMessage['email_message_id']) && !empty($originalMessage['email_message_id'])) {
                $this->mail->addCustomHeader('In-Reply-To', $originalMessage['email_message_id']);
                $this->mail->addCustomHeader('References', $originalMessage['email_message_id']);
            }

            // Email content
            $this->mail->isHTML(true);
            $this->mail->Subject = 'Re: ' . $originalMessage['subject'];

            // Use simple template like working tests
            $adminName = $adminUser['username'] ?? 'Meleva Tours Team';
            $this->mail->Body = $this->getSimpleReplyTemplate($originalMessage, $replyContent, $adminName, $messageType);
            $this->mail->AltBody = $this->getSimpleReplyPlainText($originalMessage, $replyContent, $adminName);

            $emailSent = $this->mail->send();

            // Track the email in database
            if ($emailSent) {
                $this->trackOutgoingEmail($originalMessage, $messageId, $conversationId, $replyContent, $adminUser);
            }

            return $emailSent;
        } catch (Exception $e) {
            error_log("Reply email error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate unique conversation ID
     */
    private function generateConversationId($originalMessage) {
        if (isset($originalMessage['conversation_id']) && !empty($originalMessage['conversation_id'])) {
            return $originalMessage['conversation_id'];
        }

        // Generate new conversation ID based on original message
        return 'CONV_' . strtoupper(substr(md5($originalMessage['sender_email'] . $originalMessage['subject']), 0, 12));
    }

    /**
     * Generate unique email message ID
     */
    private function generateEmailMessageId() {
        return '<' . uniqid() . '@melevatours.co.ke>';
    }

    /**
     * Track quote-related email in database
     */
    private function trackQuoteEmail($quoteData, $messageId, $conversationId, $emailType) {
        try {
            $db = Database::getInstance()->getConnection();

            // Handle different email types with appropriate subjects
            $subject = '';
            $messageContent = '';

            switch ($emailType) {
                case 'quote_sent':
                    $subject = 'Your Travel Quote is Ready - ' . $quoteData['quote_reference'];
                    $messageContent = 'Quote sent to customer with reference: ' . $quoteData['quote_reference'];
                    break;
                case 'quote_updated':
                    $subject = 'Quote Updated - ' . $quoteData['quote_reference'];
                    $messageContent = 'Quote amount updated for reference: ' . $quoteData['quote_reference'];
                    break;
                case 'partial_payment_received':
                    $subject = 'Payment Received - ' . $quoteData['quote_reference'];
                    $messageContent = 'Partial payment received for quote: ' . $quoteData['quote_reference'];
                    break;
                default:
                    $subject = 'Quote Communication - ' . $quoteData['quote_reference'];
                    $messageContent = 'Email sent regarding quote: ' . $quoteData['quote_reference'];
            }

            // Insert the outgoing message
            $sql = "INSERT INTO messages (
                conversation_id, email_message_id, message_type, quote_id,
                sender_name, sender_email, subject, message_content,
                email_status, is_read
            ) VALUES (
                :conversation_id, :email_message_id, 'outgoing', :quote_id,
                :sender_name, :sender_email, :subject, :message_content,
                'sent', 1
            )";

            $stmt = $db->prepare($sql);
            $stmt->execute([
                ':conversation_id' => $conversationId,
                ':email_message_id' => $messageId,
                ':quote_id' => $quoteData['quote_id'] ?? null,
                ':sender_name' => 'Meleva Tours and Travel',
                ':sender_email' => '<EMAIL>',
                ':subject' => $subject,
                ':message_content' => $messageContent
            ]);

        } catch (Exception $e) {
            error_log("Quote email tracking error: " . $e->getMessage());
        }
    }

    /**
     * Track outgoing email in database
     */
    private function trackOutgoingEmail($originalMessage, $messageId, $conversationId, $replyContent, $adminUser) {
        try {
            $db = Database::getInstance()->getConnection();

            // Insert the outgoing message
            $sql = "INSERT INTO messages (
                conversation_id, email_message_id, in_reply_to, message_type,
                sender_name, sender_email, subject, message_content,
                email_status, original_message_id, is_read
            ) VALUES (
                :conversation_id, :email_message_id, :in_reply_to, 'outgoing',
                :sender_name, :sender_email, :subject, :message_content,
                'sent', :original_message_id, 1
            )";

            $stmt = $db->prepare($sql);
            $stmt->execute([
                ':conversation_id' => $conversationId,
                ':email_message_id' => $messageId,
                ':in_reply_to' => $originalMessage['email_message_id'] ?? null,
                ':sender_name' => $adminUser['username'] ?? 'Meleva Tours Support',
                ':sender_email' => '<EMAIL>',
                ':subject' => 'Re: ' . $originalMessage['subject'],
                ':message_content' => $replyContent,
                ':original_message_id' => $originalMessage['message_id'] ?? null
            ]);

            $outgoingMessageId = $db->lastInsertId();

            // Update conversation
            $this->updateConversation($conversationId, $originalMessage, 'pending');

            // Track email delivery
            $this->trackEmailDelivery($outgoingMessageId, $messageId);

        } catch (Exception $e) {
            error_log("Email tracking error: " . $e->getMessage());
        }
    }

    /**
     * Update conversation status and activity
     */
    private function updateConversation($conversationId, $originalMessage, $status = 'pending') {
        try {
            $db = Database::getInstance()->getConnection();

            // Check if conversation exists
            $checkSql = "SELECT conversation_id FROM email_conversations WHERE conversation_id = :conversation_id";
            $checkStmt = $db->prepare($checkSql);
            $checkStmt->execute([':conversation_id' => $conversationId]);

            if ($checkStmt->fetch()) {
                // Update existing conversation
                $updateSql = "UPDATE email_conversations SET
                    status = :status,
                    last_activity = NOW()
                    WHERE conversation_id = :conversation_id";
                $updateStmt = $db->prepare($updateSql);
                $updateStmt->execute([
                    ':status' => $status,
                    ':conversation_id' => $conversationId
                ]);
            } else {
                // Create new conversation
                $insertSql = "INSERT INTO email_conversations (
                    conversation_id, initial_message_id, sender_email, sender_name,
                    subject, status, last_activity
                ) VALUES (
                    :conversation_id, :initial_message_id, :sender_email, :sender_name,
                    :subject, :status, NOW()
                )";
                $insertStmt = $db->prepare($insertSql);
                $insertStmt->execute([
                    ':conversation_id' => $conversationId,
                    ':initial_message_id' => $originalMessage['message_id'] ?? 0,
                    ':sender_email' => $originalMessage['sender_email'],
                    ':sender_name' => $originalMessage['sender_name'],
                    ':subject' => $originalMessage['subject'],
                    ':status' => $status
                ]);
            }
        } catch (Exception $e) {
            error_log("Conversation update error: " . $e->getMessage());
        }
    }

    /**
     * Track email delivery status
     */
    private function trackEmailDelivery($messageId, $emailMessageId) {
        try {
            $db = Database::getInstance()->getConnection();

            $sql = "INSERT INTO email_tracking (
                message_id, email_message_id, status, sent_at
            ) VALUES (
                :message_id, :email_message_id, 'sent', NOW()
            )";

            $stmt = $db->prepare($sql);
            $stmt->execute([
                ':message_id' => $messageId,
                ':email_message_id' => $emailMessageId
            ]);
        } catch (Exception $e) {
            error_log("Email delivery tracking error: " . $e->getMessage());
        }
    }

    /**
     * Get simple reply email HTML template (like working tests)
     */
    private function getSimpleReplyTemplate($originalMessage, $replyContent, $adminName, $messageType) {
        $fromDept = $messageType === 'quote' ? 'Booking Department' : 'Customer Service';

        return "
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Reply from Meleva Tours</title>
        </head>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h2 style='color: #dc2626;'>Reply from Meleva Tours - {$fromDept}</h2>

                <div style='background: #f9f9f9; padding: 15px; border-left: 4px solid #dc2626; margin: 20px 0;'>
                    <p><strong>Your Message:</strong></p>
                    <p>" . nl2br(htmlspecialchars($originalMessage['message_content'])) . "</p>
                </div>

                <div style='background: #fff; padding: 15px; border: 1px solid #ddd; margin: 20px 0;'>
                    <p><strong>Our Reply:</strong></p>
                    <p>" . nl2br(htmlspecialchars($replyContent)) . "</p>
                </div>

                <p><strong>Best regards,</strong><br>
                {$adminName}<br>
                Meleva Tours and Travel<br>
                <em><small>Smooth Travels, Seamless Experiences!</small></em></p>

                <hr style='margin: 30px 0;'>
                <p style='font-size: 12px; color: #666;'>
                    <strong>Meleva Tours and Travel</strong><br>
                    Email: <EMAIL><br>
                    Phone: +254 123 456 789
                </p>
            </div>
        </body>
        </html>";
    }

    /**
     * Get simple reply email plain text
     */
    private function getSimpleReplyPlainText($originalMessage, $replyContent, $adminName) {
        return "Reply from Meleva Tours\n\n" .
               "Your Message:\n" . $originalMessage['message_content'] . "\n\n" .
               "Our Reply:\n" . $replyContent . "\n\n" .
               "Best regards,\n" .
               $adminName . "\n" .
               "Meleva Tours and Travel\n" .
               "Smooth Travels, Seamless Experiences!\n\n" .
               "---\n" .
               "Meleva Tours and Travel\n" .
               "Email: <EMAIL>\n" .
               "Phone: +254 123 456 789";
    }

    /**
     * Get reply email HTML template (DEPRECATED - keeping for compatibility)
     */
    private function getReplyEmailTemplate($originalMessage, $replyContent, $adminUser, $messageType) {
        $contactInfo = $this->contactModel->getCurrent();
        $primaryColor = $messageType === 'quote' ? '#dc2626' : '#f97316';
        $adminName = $adminUser['username'] ?? 'Meleva Tours Team';

        return "
        <html>
        <head>
            <title>Response to Your Inquiry - Meleva Tours and Travel</title>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; background: #ffffff; }
                .header { background: linear-gradient(135deg, {$primaryColor}, #ea580c); color: white; padding: 25px; text-align: center; border-radius: 8px 8px 0 0; }
                .content { background: #f8fafc; padding: 25px; border-radius: 0 0 8px 8px; border: 1px solid #e2e8f0; }
                .reply-box { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid {$primaryColor}; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .original-message { background: #f1f5f9; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 3px solid #64748b; }
                .contact-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e2e8f0; }
                .contact-item { display: flex; align-items: center; margin-bottom: 10px; }
                .contact-icon { width: 20px; margin-right: 10px; color: {$primaryColor}; }
                .signature { margin-top: 20px; padding-top: 15px; border-top: 1px solid #e2e8f0; }
                " . $this->getCommonEmailStyles() . "
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <div class='logo'>
                        <img src='" . $this->getLogoBase64() . "' alt='Meleva Tours and Travel'>
                    </div>
                    <h2>Reply</h2>
                </div>
                <div class='content'>
                    <p>Dear <strong>{$originalMessage['sender_name']}</strong>,</p>

                    <p>Thank you for your message. We have reviewed your inquiry and here is our response:</p>

                    <div class='reply-box'>
                        <h3 style='color: {$primaryColor}; margin-top: 0;'> Our Response:</h3>
                        <p style='margin-bottom: 0;'>" . nl2br(htmlspecialchars($replyContent)) . "</p>
                    </div>

                    <div class='original-message'>
                        <h4 style='margin-top: 0; color: #64748b;'> Your Original Message:</h4>
                        <p><strong>Subject:</strong> {$originalMessage['subject']}</p>
                        <p style='margin-bottom: 0;'>" . nl2br(htmlspecialchars(substr($originalMessage['message_content'], 0, 300))) .
                        (strlen($originalMessage['message_content']) > 300 ? '...' : '') . "</p>
                    </div>

                    <p>If you have any follow-up questions or need further assistance, please don't hesitate to contact us using the information below.</p>

                    <div class='contact-info'>
                        <h3 style='color: {$primaryColor}; margin-top: 0;'>Contact Information</h3>
                        <div class='contact-item'>
                            <span class='contact-icon'></span>
                            <span><strong>Phone:</strong> {$contactInfo['phone_number']}</span>
                        </div>
                        <div class='contact-item'>
                            <span class='contact-icon'></span>
                            <span><strong>Email:</strong> " . ($contactInfo['booking_email'] ?? '<EMAIL>') . "</span>
                        </div>
                        <div class='contact-item'>
                            <span class='contact-icon'></span>
                            <span><strong>Location:</strong> {$contactInfo['address']}</span>
                        </div>
                        <div class='contact-item'>
                            <span class='contact-icon'></span>
                            <span><strong>Hours:</strong> {$contactInfo['working_hours']}</span>
                        </div>
                    </div>

                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='" . $this->getBaseUrl() . "/' style='display: inline-block; background: {$primaryColor}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>Visit Our Website</a>
                        <a href='" . $this->getBaseUrl() . "/tours.php' style='display: inline-block; background: {$primaryColor}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>View Tours</a>
                        <a href='" . $this->getBaseUrl() . "/contact.php' style='display: inline-block; background: {$primaryColor}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>Contact Us</a>
                    </div>

                    <div class='signature'>
                        <p><strong>Best regards,</strong><br>
                        <span style='color: {$primaryColor}; font-weight: bold;'>{$adminName}</span><br>
                        Meleva Tours and Travel<br>
                        <em><small>Smooth Travels, Seamless Experiences!</small></em></p>
                    </div>
                </div>
                <div class='footer'>
                    <p>You can reply directly to this email for further assistance.</p>
                    <p>This message was sent in response to your inquiry on our website.</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Get reply email plain text version
     */
    private function getReplyEmailPlainText($originalMessage, $replyContent, $adminUser) {
        $contactInfo = $this->contactModel->getCurrent();
        $adminName = $adminUser['username'] ?? 'Meleva Tours Team';

        return "MELEVA TOURS AND TRAVEL - Response to Your Inquiry\n\n" .
               "Dear {$originalMessage['sender_name']},\n\n" .
               "Thank you for your message. We have reviewed your inquiry and here is our response:\n\n" .
               "OUR RESPONSE:\n" .
               str_repeat('-', 50) . "\n" .
               $replyContent . "\n" .
               str_repeat('-', 50) . "\n\n" .
               "YOUR ORIGINAL MESSAGE:\n" .
               "Subject: {$originalMessage['subject']}\n" .
               substr($originalMessage['message_content'], 0, 300) .
               (strlen($originalMessage['message_content']) > 300 ? '...' : '') . "\n\n" .
               "If you have any follow-up questions or need further assistance, please contact us:\n\n" .
               "CONTACT INFORMATION:\n" .
               "Phone: {$contactInfo['phone_number']}\n" .
               "Email: " . ($contactInfo['booking_email'] ?? '<EMAIL>') . "\n" .
               "Location: {$contactInfo['address']}\n" .
               "Hours: {$contactInfo['working_hours']}\n\n" .
               "QUICK LINKS:\n" .
               "Website: " . $this->getBaseUrl() . "/\n" .
               "View Tours: " . $this->getBaseUrl() . "/tours.php\n" .
               "Contact Us: " . $this->getBaseUrl() . "/contact.php\n\n" .
               "Best regards,\n" .
               "{$adminName}\n" .
               "Meleva Tours and Travel\n" .
               "Smooth Travels, Seamless Experiences!\n\n" .
               "You can reply directly to this email for further assistance.";
    }

    /**
     * Send additional email to user (for follow-ups, updates, etc.)
     */
    public function sendAdditionalEmail($originalMessage, $emailContent, $adminUser) {
        try {
            // Clear any previous recipients and set sender
            $this->mail->clearAddresses();
            $this->mail->clearReplyTos();

            // Determine message type for appropriate sender/reply-to
            $messageType = strpos(strtolower($originalMessage['subject']), 'quote') !== false ? 'quote' : 'contact';

            if ($messageType === 'quote') {
                // Quote-related additional emails - update SMTP credentials to match sender
                $this->mail->Username = '<EMAIL>';
                $this->mail->Password = 'hi$Ch9=lYcap{7cA';
                $this->mail->setFrom('<EMAIL>', 'Meleva Tours - Booking Department');
                $this->mail->addReplyTo('<EMAIL>', 'Meleva Tours - Booking Department');
            } else {
                // Contact-related additional emails - use default info credentials
                $this->mail->Username = '<EMAIL>';
                $this->mail->Password = 'hi$Ch9=lYcap{7cA';
                $this->mail->setFrom('<EMAIL>', 'Meleva Tours - Customer Service');
                $this->mail->addReplyTo('<EMAIL>', 'Meleva Tours - Customer Service');
            }

            $this->mail->addAddress($originalMessage['sender_email'], $originalMessage['sender_name']);

            // Determine message type for styling
            $messageType = strpos(strtolower($originalMessage['subject']), 'quote') !== false ? 'quote' : 'contact';

            // Email content
            $this->mail->isHTML(true);
            $this->mail->Subject = 'Follow-up: ' . $originalMessage['subject'];

            $this->mail->Body = $this->getAdditionalEmailTemplate($originalMessage, $emailContent, $adminUser, $messageType);
            $this->mail->AltBody = $this->getAdditionalEmailPlainText($originalMessage, $emailContent, $adminUser);

            return $this->mail->send();
        } catch (Exception $e) {
            error_log("Additional email error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get additional email HTML template (without emojis)
     */
    private function getAdditionalEmailTemplate($originalMessage, $emailContent, $adminUser, $messageType) {
        $contactInfo = $this->contactModel->getCurrent();
        $primaryColor = $messageType === 'quote' ? '#dc2626' : '#f97316';
        $adminName = $adminUser['username'] ?? 'Meleva Tours Team';

        return "
        <html>
        <head>
            <title>Follow-up from Meleva Tours and Travel</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; background: #ffffff; }
                .header { background: linear-gradient(135deg, {$primaryColor}, #ea580c); color: white; padding: 25px; text-align: center; border-radius: 8px 8px 0 0; }
                .content { background: #f8fafc; padding: 25px; border-radius: 0 0 8px 8px; border: 1px solid #e2e8f0; }
                .message-box { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid {$primaryColor}; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .contact-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e2e8f0; }
                .contact-item { display: flex; align-items: center; margin-bottom: 10px; }
                .contact-icon { width: 20px; margin-right: 10px; color: {$primaryColor}; }
                .signature { margin-top: 20px; padding-top: 15px; border-top: 1px solid #e2e8f0; }
                " . $this->getCommonEmailStyles() . "
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <div class='logo'>
                        <img src='" . $this->getLogoBase64() . "' alt='Meleva Tours and Travel'>
                    </div>
                    <h2>Follow-up Message</h2>
                </div>
                <div class='content'>
                    <p>Dear <strong>{$originalMessage['sender_name']}</strong>,</p>

                    <div class='message-box'>
                        <p>" . nl2br(htmlspecialchars($emailContent)) . "</p>
                    </div>

                    <p>If you have any questions or need further assistance, please don't hesitate to contact us using the information below.</p>

                    <div class='contact-info'>
                        <h3 style='color: {$primaryColor}; margin-top: 0;'>Contact Information</h3>
                        <div class='contact-item'>
                            <span class='contact-icon'></span>
                            <span><strong>Phone:</strong> {$contactInfo['phone_number']}</span>
                        </div>
                        <div class='contact-item'>
                            <span class='contact-icon'></span>
                            <span><strong>Email:</strong> " . ($contactInfo['booking_email'] ?? '<EMAIL>') . "</span>
                        </div>
                        <div class='contact-item'>
                            <span class='contact-icon'></span>
                            <span><strong>Location:</strong> {$contactInfo['address']}</span>
                        </div>
                        <div class='contact-item'>
                            <span class='contact-icon'></span>
                            <span><strong>Hours:</strong> {$contactInfo['working_hours']}</span>
                        </div>
                    </div>

                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='" . $this->getBaseUrl() . "/' style='display: inline-block; background: {$primaryColor}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>Visit Our Website</a>
                        <a href='" . $this->getBaseUrl() . "/tours.php' style='display: inline-block; background: {$primaryColor}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>View Tours</a>
                        <a href='" . $this->getBaseUrl() . "/contact.php' style='display: inline-block; background: {$primaryColor}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>Contact Us</a>
                    </div>

                    <div class='signature'>
                        <p><strong>Best regards,</strong><br>
                        <span style='color: {$primaryColor}; font-weight: bold;'>{$adminName}</span><br>
                        Meleva Tours and Travel<br>
                        <em><small>Smooth Travels, Seamless Experiences!</small></em></p>
                    </div>
                </div>
                <div class='footer'>
                    <p>You can reply directly to this email for further assistance.</p>
                    <p>This is a follow-up message regarding your inquiry with Meleva Tours and Travel.</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Get additional email plain text version
     */
    private function getAdditionalEmailPlainText($originalMessage, $emailContent, $adminUser) {
        $contactInfo = $this->contactModel->getCurrent();
        $adminName = $adminUser['username'] ?? 'Meleva Tours Team';

        return "MELEVA TOURS AND TRAVEL - Follow-up Message\n\n" .
               "Dear {$originalMessage['sender_name']},\n\n" .
               $emailContent . "\n\n" .
               "If you have any questions or need further assistance, please contact us:\n\n" .
               "CONTACT INFORMATION:\n" .
               "Phone: {$contactInfo['phone_number']}\n" .
               "Email: " . ($contactInfo['booking_email'] ?? '<EMAIL>') . "\n" .
               "Location: {$contactInfo['address']}\n" .
               "Hours: {$contactInfo['working_hours']}\n\n" .
               "QUICK LINKS:\n" .
               "Website: " . $this->getBaseUrl() . "/\n" .
               "View Tours: " . $this->getBaseUrl() . "/tours.php\n" .
               "Contact Us: " . $this->getBaseUrl() . "/contact.php\n\n" .
               "Best regards,\n" .
               "{$adminName}\n" .
               "Meleva Tours and Travel\n" .
               "Smooth Travels, Seamless Experiences!\n\n" .
               "You can reply directly to this email for further assistance.";
    }





    /**
     * Get quote request notification HTML template (for admin)
     */
    private function getQuoteRequestNotificationTemplate($data) {
        $packagesList = '';
        if (!empty($data['selected_packages'])) {
            $packagesList = '<ul>';
            foreach ($data['selected_packages'] as $packageId) {
                // You might want to fetch package names here
                $packagesList .= '<li>Package ID: ' . htmlspecialchars($packageId) . '</li>';
            }
            $packagesList .= '</ul>';
        }

        $activitiesList = '';
        if (!empty($data['selected_activities'])) {
            $activitiesList = '<ul>';
            foreach ($data['selected_activities'] as $activity) {
                $activitiesList .= '<li>' . htmlspecialchars($activity) . '</li>';
            }
            $activitiesList .= '</ul>';
        }

        return "
        <html>
        <head>
            <title>New Quote Request - {$data['quote_reference']}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; background: #ffffff; }
                .header { background: linear-gradient(135deg, #dc2626, #ea580c); color: white; padding: 25px; text-align: center; border-radius: 8px 8px 0 0; }
                .content { background: #f8fafc; padding: 25px; border-radius: 0 0 8px 8px; border: 1px solid #e2e8f0; }
                .highlight { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #dc2626; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .customer-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e2e8f0; }
                .action-button { display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; }
                .footer { text-align: center; margin-top: 25px; color: #64748b; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>🔔 New Quote Request</h2>
                    <p style='margin: 0; font-size: 18px;'>{$data['quote_reference']}</p>
                </div>
                <div class='content'>
                    <div class='highlight'>
                        <strong>Priority:</strong> HIGH - New quote request requires attention<br>
                        <strong>Reference:</strong> {$data['quote_reference']}<br>
                        <strong>Received:</strong> " . date('F j, Y \a\t g:i A') . "
                    </div>

                    <div class='customer-info'>
                        <h3 style='color: #dc2626; margin-top: 0;'>Customer Information</h3>
                        <p><strong>Name:</strong> {$data['customer_name']}</p>
                        <p><strong>Email:</strong> <a href='mailto:{$data['customer_email']}'>{$data['customer_email']}</a></p>
                        <p><strong>Phone:</strong> <a href='tel:{$data['customer_phone']}'>{$data['customer_phone']}</a></p>
                        <p><strong>Country:</strong> {$data['customer_country']}</p>
                    </div>

                    <div class='customer-info'>
                        <h3 style='color: #dc2626; margin-top: 0;'>Travel Details</h3>
                        <p><strong>Travel Date:</strong> " . ($data['travel_date'] ? date('F j, Y', strtotime($data['travel_date'])) : 'To be determined') . "</p>
                        <p><strong>Travelers:</strong> {$data['number_of_adults']} Adult(s)" . ($data['number_of_children'] > 0 ? ", {$data['number_of_children']} Child(ren)" : "") . "</p>

                        " . (!empty($packagesList) ? "<p><strong>Selected Packages:</strong></p>" . $packagesList : "") . "
                        " . (!empty($activitiesList) ? "<p><strong>Interested Activities:</strong></p>" . $activitiesList : "") . "
                        " . (!empty($data['special_requirements']) ? "<p><strong>Special Requirements:</strong><br>" . nl2br(htmlspecialchars($data['special_requirements'])) . "</p>" : "") . "
                    </div>

                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='" . $this->getBaseUrl() . "/admin-dashboard/quotes.php' class='action-button'>
                            📋 Manage Quotes
                        </a>
                        <a href='mailto:{$data['customer_email']}?subject=Re: Your Travel Quote Request - {$data['quote_reference']}' class='action-button'>
                            ✉️ Reply to Customer
                        </a>
                    </div>

                    <p><strong>Next Steps:</strong></p>
                    <ol>
                        <li>Review the customer's requirements</li>
                        <li>Prepare a detailed quote with pricing</li>
                        <li>Send the quote via email with payment link</li>
                        <li>Follow up if needed</li>
                    </ol>
                </div>
                <div class='footer'>
                    <p>This is an automated notification from Meleva Tours Admin System</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Get quote request notification plain text template (for admin)
     */
    private function getQuoteRequestNotificationPlainText($data) {
        $packagesList = '';
        if (!empty($data['selected_packages'])) {
            $packagesList = "\nSelected Packages:\n";
            foreach ($data['selected_packages'] as $packageId) {
                $packagesList .= "- Package ID: " . $packageId . "\n";
            }
        }

        $activitiesList = '';
        if (!empty($data['selected_activities'])) {
            $activitiesList = "\nInterested Activities:\n";
            foreach ($data['selected_activities'] as $activity) {
                $activitiesList .= "- " . $activity . "\n";
            }
        }

        return "MELEVA TOURS ADMIN - NEW QUOTE REQUEST\n\n" .
               "🔔 HIGH PRIORITY - New quote request requires attention\n\n" .
               "QUOTE REFERENCE: {$data['quote_reference']}\n" .
               "RECEIVED: " . date('F j, Y \a\t g:i A') . "\n\n" .
               "CUSTOMER INFORMATION:\n" .
               "Name: {$data['customer_name']}\n" .
               "Email: {$data['customer_email']}\n" .
               "Phone: {$data['customer_phone']}\n" .
               "Country: {$data['customer_country']}\n\n" .
               "TRAVEL DETAILS:\n" .
               "Travel Date: " . ($data['travel_date'] ? date('F j, Y', strtotime($data['travel_date'])) : 'To be determined') . "\n" .
               "Travelers: {$data['number_of_adults']} Adult(s)" . ($data['number_of_children'] > 0 ? ", {$data['number_of_children']} Child(ren)" : "") . "\n" .
               $packagesList .
               $activitiesList .
               (!empty($data['special_requirements']) ? "\nSpecial Requirements:\n" . $data['special_requirements'] . "\n" : "") . "\n" .
               "NEXT STEPS:\n" .
               "1. Review the customer's requirements\n" .
               "2. Prepare a detailed quote with pricing\n" .
               "3. Send the quote via email with payment link\n" .
               "4. Follow up if needed\n\n" .
               "ADMIN ACTIONS:\n" .
               "Manage Quotes: " . $this->getBaseUrl() . "/admin-dashboard/quotes.php\n" .
               "Reply to Customer: mailto:{$data['customer_email']}?subject=Re: Your Travel Quote Request - {$data['quote_reference']}\n\n" .
               "This is an automated notification from Meleva Tours Admin System";
    }

    /**
     * Get quote email HTML template (sent to customers with payment link)
     */
    private function getQuoteEmailTemplate($data, $adminUser = null) {
        $contactInfo = $this->contactModel->getCurrent();
        $paymentUrl = $this->getBaseUrl() . "/payment.php?quote_ref=" . urlencode($data['quote_reference']);
        $includePaymentLink = $data['include_payment_link'] ?? false;

        return "
        <html>
        <head>
            <title>Your Travel Quote is Ready - {$data['quote_reference']}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; background: #ffffff; }
                .header { background: linear-gradient(135deg, #dc2626, #ea580c); color: white; padding: 25px; text-align: center; border-radius: 8px 8px 0 0; }
                .content { background: #f8fafc; padding: 25px; border-radius: 0 0 8px 8px; border: 1px solid #e2e8f0; }
                .highlight { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #dc2626; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .quote-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e2e8f0; }
                .payment-section { background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #f59e0b; text-align: center; }
                .payment-button { display: inline-block; background: linear-gradient(135deg, #f59e0b, #ea580c); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; margin: 10px 0; transition: all 0.3s ease; }
                .contact-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e2e8f0; }
                " . $this->getCommonEmailStyles() . "
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <div class='logo'>
                        <img src='" . $this->getLogoBase64() . "' alt='Meleva Tours and Travel'>
                    </div>
                    <h2>Your Travel Quote is Ready!</h2>
                </div>
                <div class='content'>
                    <p>Dear <strong>{$data['customer_name']}</strong>,</p>

                    <p>Great news! We've prepared your personalized travel quote and it's ready for your review. Our safari specialists have carefully crafted this proposal based on your requirements.</p>

                    <div class='highlight'>
                        <strong>Quote Reference:</strong> {$data['quote_reference']}<br>
                        <strong>Total Amount:</strong> <span style='font-size: 24px; color: #dc2626;'>$" . number_format($data['quoted_amount'], 2) . "</span><br>
                        <strong>Status:</strong> Ready for Payment
                    </div>

                    <div class='quote-details'>
                        <h3 style='color: #dc2626; margin-top: 0;'>Your Travel Details</h3>
                        <p><strong>Travel Date:</strong> " . ($data['travel_date'] ? date('F j, Y', strtotime($data['travel_date'])) : 'To be determined') . "</p>
                        <p><strong>Travelers:</strong> {$data['number_of_adults']} Adult(s)" . ($data['number_of_children'] > 0 ? ", {$data['number_of_children']} Child(ren)" : "") . "</p>
                        " . (!empty($data['special_requirements']) ? "<p><strong>Special Requirements:</strong><br>" . nl2br(htmlspecialchars($data['special_requirements'])) . "</p>" : "") . "

                        <h4 style='color: #dc2626; margin-top: 20px;'>Quote Details:</h4>
                        <div style='background: #f9fafb; padding: 15px; border-radius: 6px; border-left: 3px solid #dc2626;'>
                            " . nl2br(htmlspecialchars($data['quote_details'])) . "
                        </div>
                    </div>

                    " . ($includePaymentLink ? "
                    <div class='payment-section'>
                        <h3 style='color: #f59e0b; margin-top: 0;'>Ready to Book Your Adventure?</h3>
                        <p>You can now proceed with payment for your safari adventure!</p>
                        <div style='background: white; padding: 15px; border-radius: 6px; margin: 15px 0; border: 1px solid #e5e7eb;'>
                            <p style='margin: 5px 0; font-size: 14px;'><strong>Total Quote:</strong> $" . number_format($data['quoted_amount'], 2) . " USD</p>
                            <p style='margin: 5px 0; font-size: 12px; color: #6b7280;'>You can pay any amount up to the full quote amount</p>
                        </div>
                        <a href='{$paymentUrl}' class='payment-button'>
                            Proceed to Payment
                        </a>
                        <p style='font-size: 12px; color: #6b7280; margin-top: 15px;'>
                            Secure payment powered by Pesapal. Multiple payment methods accepted including M-Pesa, cards, and bank transfers.
                        </p>
                    </div>
                    " : "
                    <div class='contact-info' style='background: #fef3c7; border: 2px solid #f59e0b;'>
                        <h3 style='color: #f59e0b; margin-top: 0;'>Next Steps</h3>
                        <p>Please review the quote details above. If you're satisfied with the proposal, reply to this email to confirm your agreement.</p>
                        <p>Once we receive your confirmation, we'll send you a secure payment link to complete your booking.</p>
                        <p style='font-size: 12px; color: #6b7280;'>This ensures we're both aligned on all details before proceeding with payment.</p>
                    </div>
                    ") . "

                    <div class='contact-info'>
                        <h3 style='color: #dc2626; margin-top: 0;'>Questions About Your Quote?</h3>
                        <p>Our team is here to help! Feel free to reach out if you need any clarifications or modifications:</p>
                        <div class='contact-item' style='margin: 10px 0;'>
                            <span class='contact-icon'></span>
                            <span><strong>Phone:</strong> <a href='tel:{$contactInfo['phone_number']}' style='color: #dc2626; text-decoration: none;'>{$contactInfo['phone_number']}</a></span>
                        </div>
                        <div class='contact-item' style='margin: 10px 0;'>
                            <span class='contact-icon'></span>
                            <span><strong>Email:</strong> <a href='mailto:" . ($contactInfo['booking_email'] ?? '<EMAIL>') . "' style='color: #dc2626; text-decoration: none;'>" . ($contactInfo['booking_email'] ?? '<EMAIL>') . "</a></span>
                        </div>
                        <div class='contact-item' style='margin: 10px 0;'>
                            <span class='contact-icon'></span>
                            <span><strong>Location:</strong> {$contactInfo['address']}</span>
                        </div>
                        <div class='contact-item' style='margin: 10px 0;'>
                            <span class='contact-icon'></span>
                            <span><strong>Hours:</strong> {$contactInfo['working_hours']}</span>
                        </div>
                        <p style='font-size: 12px; color: #6b7280; margin-top: 15px;'>Simply reply to this email and we'll get back to you promptly.</p>
                    </div>

                    <p>We're excited to help you create unforgettable memories in Kenya!</p>

                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='" . $this->getBaseUrl() . "/' style='display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>Visit Our Website</a>
                        <a href='" . $this->getBaseUrl() . "/tours.php' style='display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>View Tours</a>
                        <a href='" . $this->getBaseUrl() . "/contact.php' style='display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>Contact Us</a>
                    </div>

                    <div class='signature'>
                        <p><strong>Best regards,</strong><br>
                        <span style='color: #dc2626; font-weight: bold;'>" . ($adminUser ? htmlspecialchars($adminUser['full_name'] ?? $adminUser['username']) : 'Meleva Tours Team') . "</span><br>
                        Meleva Tours and Travel<br>
                        <em><small>Smooth Travels, Seamless Experiences!</small></em></p>
                    </div>
                </div>
                <div class='footer'>
                    <p>You can reply directly to this email for further assistance.</p>
                    <p>This quote was prepared specifically for your travel requirements.</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Get quote email plain text template (sent to customers with payment link)
     */
    private function getQuoteEmailPlainText($data, $adminUser = null) {
        $contactInfo = $this->contactModel->getCurrent();
        $paymentUrl = $this->getBaseUrl() . "/payment.php?quote_ref=" . urlencode($data['quote_reference']);
        $includePaymentLink = $data['include_payment_link'] ?? false;

        return "MELEVA TOURS AND TRAVEL - Your Travel Quote is Ready!\n\n" .
               "Dear {$data['customer_name']},\n\n" .
               "We've prepared your personalized travel quote and it's ready for your review. Our safari specialists have carefully crafted this proposal based on your requirements.\n\n" .
               "QUOTE SUMMARY:\n" .
               "Reference: {$data['quote_reference']}\n" .
               "Total Amount: $" . number_format($data['quoted_amount'], 2) . "\n" .
               "Status: " . ($includePaymentLink ? "Ready for Payment" : "Awaiting Your Confirmation") . "\n\n" .
               "YOUR TRAVEL DETAILS:\n" .
               "Travel Date: " . ($data['travel_date'] ? date('F j, Y', strtotime($data['travel_date'])) : 'To be determined') . "\n" .
               "Travelers: {$data['number_of_adults']} Adult(s)" . ($data['number_of_children'] > 0 ? ", {$data['number_of_children']} Child(ren)" : "") . "\n" .
               (!empty($data['special_requirements']) ? "Special Requirements: " . $data['special_requirements'] . "\n" : "") . "\n" .
               "QUOTE DETAILS:\n" .
               $data['quote_details'] . "\n\n" .
               ($includePaymentLink ?
                   "READY TO BOOK YOUR ADVENTURE?\n" .
                   "You can now proceed with payment for your safari adventure!\n\n" .
                   "PAYMENT OPTIONS:\n" .
                   "Total Quote: $" . number_format($data['quoted_amount'], 2) . " USD\n" .
                   "You can pay any amount up to the full quote amount\n\n" .
                   "PROCEED TO PAYMENT:\n" .
                   $paymentUrl . "\n\n" .
                   "Secure payment powered by Pesapal. Multiple payment methods accepted including M-Pesa, cards, and bank transfers.\n\n"
                   :
                   "NEXT STEPS:\n" .
                   "Please review the quote details above. If you're satisfied with the proposal, reply to this email to confirm your agreement.\n\n" .
                   "Once we receive your confirmation, we'll send you a secure payment link to complete your booking.\n\n" .
                   "This ensures we're both aligned on all details before proceeding with payment.\n\n"
               ) .
               "QUESTIONS ABOUT YOUR QUOTE?\n" .
               "Our team is here to help! Feel free to reach out if you need any clarifications or modifications:\n" .
               "Phone: {$contactInfo['phone_number']}\n" .
               "Email: " . ($contactInfo['booking_email'] ?? '<EMAIL>') . "\n" .
               "Location: {$contactInfo['address']}\n" .
               "Hours: {$contactInfo['working_hours']}\n" .
               "Simply reply to this email and we'll get back to you promptly.\n\n" .

               "We're excited to help you create unforgettable memories in Kenya!\n\n" .
               "VISIT OUR WEBSITE | VIEW TOURS | CONTACT US\n" .
               "Website: " . $this->getBaseUrl() . "/\n" .
               "View Tours: " . $this->getBaseUrl() . "/tours.php\n" .
               "Contact Us: " . $this->getBaseUrl() . "/contact.php\n\n" .
               "Best regards,\n" .
               ($adminUser ? ($adminUser['full_name'] ?? $adminUser['username']) . "\n" : "Meleva Tours Team\n") .
               "Meleva Tours and Travel\n" .
               "Smooth Travels, Seamless Experiences!\n\n" .
               "You can reply directly to this email for further assistance.\n" .
               "This quote was prepared specifically for your travel requirements.";
    }



    /**
     * Send payment receipt email to customer
     */
    public function sendPaymentReceipt($paymentData) {
        try {
            // Clear any previous recipients and set sender
            $this->mail->clearAddresses();
            $this->mail->clearReplyTos();

            $this->mail->setFrom(
                $this->emailConfig['user_confirmations']['from_email'],
                $this->emailConfig['user_confirmations']['from_name']
            );

            // Set reply-to as booking email since payment receipts are booking-related
            $this->mail->addReplyTo('<EMAIL>', 'Meleva Tours & Travel - Booking Department');

            $this->mail->addAddress($paymentData['customer_email'], $paymentData['customer_name']);

            // Email content
            $this->mail->isHTML(true);
            $this->mail->Subject = 'Payment Receipt - ' . $paymentData['quote_reference'] . ' | Meleva Tours';

            $this->mail->Body = $this->getPaymentReceiptTemplate($paymentData);
            $this->mail->AltBody = $this->getPaymentReceiptPlainText($paymentData);

            return $this->mail->send();
        } catch (Exception $e) {
            error_log("Payment receipt email error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get payment receipt HTML template
     */
    private function getPaymentReceiptTemplate($data) {
        $contactInfo = $this->contactModel->getCurrent();

        return "
        <html>
        <head>
            <title>Payment Receipt - {$data['quote_reference']}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; background: #ffffff; }
                .header { background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 25px; text-align: center; border-radius: 8px 8px 0 0; }
                .content { background: #f8fafc; padding: 25px; border-radius: 0 0 8px 8px; border: 1px solid #e2e8f0; }
                .highlight { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #10b981; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .receipt-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e2e8f0; }
                .amount-paid { background: #d1fae5; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #10b981; text-align: center; }
                .contact-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e2e8f0; }
                " . $this->getCommonEmailStyles() . "
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <div class='logo'>
                        <img src='" . $this->getLogoBase64() . "' alt='Meleva Tours and Travel'>
                    </div>
                    <h2>Payment Received!</h2>
                </div>
                <div class='content'>
                    <p>Dear <strong>{$data['customer_name']}</strong>,</p>

                    <p>Thank you for your payment! We have successfully received your payment for your travel booking. Your adventure with Meleva Tours is now confirmed!</p>

                    <div class='receipt-details' style='background: white; padding: 25px; border-radius: 8px; margin: 20px 0; border: 2px solid #10b981; box-shadow: 0 4px 6px rgba(0,0,0,0.1);'>
                        <h3 style='color: #10b981; margin-top: 0; text-align: center; border-bottom: 2px solid #10b981; padding-bottom: 10px;'>🧾 Payment Receipt</h3>

                        <!-- Customer & Transaction Info -->
                        <div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; padding: 15px; background: #f8fafc; border-radius: 6px;'>
                            <div>
                                <p style='margin: 5px 0;'><strong>Customer:</strong> {$data['customer_name']}</p>
                                <p style='margin: 5px 0;'><strong>Quote Reference:</strong> {$data['quote_reference']}</p>
                                <p style='margin: 5px 0;'><strong>Payment Reference:</strong> {$data['payment_reference']}</p>
                            </div>
                            <div>
                                <p style='margin: 5px 0;'><strong>Transaction Date:</strong> " . date('F j, Y') . "</p>
                                <p style='margin: 5px 0;'><strong>Transaction Time:</strong> " . date('g:i A') . "</p>
                                <p style='margin: 5px 0;'><strong>Payment Method:</strong> " . ($data['payment_method'] ?? 'Online Payment') . "</p>
                            </div>
                        </div>

                        <!-- Booking Details -->
                        <div style='margin: 20px 0; padding: 15px; background: #f0f9ff; border-radius: 6px; border-left: 4px solid #3b82f6;'>
                            <h4 style='color: #1e40af; margin-top: 0; margin-bottom: 10px;'>📋 Booking Information</h4>
                            <p style='margin: 5px 0;'><strong>Travel Date:</strong> " . ($data['travel_date'] ? date('F j, Y', strtotime($data['travel_date'])) : 'To be determined') . "</p>
                            <p style='margin: 5px 0;'><strong>Travelers:</strong> {$data['number_of_adults']} Adult(s)" . ($data['number_of_children'] > 0 ? ", {$data['number_of_children']} Child(ren)" : "") . "</p>
                        </div>

                        <!-- Payment Summary -->
                        <div style='margin: 20px 0; padding: 15px; background: #f0fdf4; border-radius: 6px; border-left: 4px solid #10b981;'>
                            <h4 style='color: #059669; margin-top: 0; margin-bottom: 10px;'>💰 Payment Summary</h4>
                            <div style='display: flex; justify-content: space-between; margin: 8px 0; padding: 5px 0;'>
                                <span>Quote Total:</span>
                                <span style='font-weight: bold;'>$" . number_format($data['quoted_amount'] ?? 0, 2) . " USD</span>
                            </div>
                            <div style='display: flex; justify-content: space-between; margin: 8px 0; padding: 5px 0; background: #dcfce7; border-radius: 4px; padding: 8px;'>
                                <span style='color: #059669; font-weight: bold;'>Amount Paid (This Transaction):</span>
                                <span style='color: #059669; font-weight: bold;'>$" . number_format($data['amount_paid'], 2) . " USD</span>
                            </div>
                            " . (isset($data['total_paid']) ? "
                            <div style='display: flex; justify-content: space-between; margin: 8px 0; padding: 5px 0; border-top: 1px solid #d1d5db;'>
                                <span>Total Amount Paid:</span>
                                <span style='font-weight: bold;'>$" . number_format($data['total_paid'], 2) . " USD</span>
                            </div>
                            " : "") . "
                            " . (isset($data['balance_remaining']) && $data['balance_remaining'] > 0 ? "
                            <div style='display: flex; justify-content: space-between; margin: 8px 0; padding: 8px; background: #fef3c7; border-radius: 4px;'>
                                <span style='color: #92400e; font-weight: bold;'>Balance Remaining:</span>
                                <span style='color: #92400e; font-weight: bold;'>$" . number_format($data['balance_remaining'], 2) . " USD</span>
                            </div>
                            " : "
                            <div style='display: flex; justify-content: space-between; margin: 8px 0; padding: 8px; background: #dcfce7; border-radius: 4px;'>
                                <span style='color: #059669; font-weight: bold;'>Payment Status:</span>
                                <span style='color: #059669; font-weight: bold;'>✅ FULLY PAID</span>
                            </div>
                            ") . "
                        </div>

                        <!-- Payment Status -->
                        <div style='text-align: center; margin: 20px 0; padding: 15px; background: " . (isset($data['balance_remaining']) && $data['balance_remaining'] > 0 ? '#fef3c7; border: 2px solid #f59e0b;' : '#dcfce7; border: 2px solid #10b981;') . " border-radius: 8px;'>
                            <p style='margin: 0; font-size: 18px; font-weight: bold; color: " . (isset($data['balance_remaining']) && $data['balance_remaining'] > 0 ? '#92400e;' : '#059669;') . "'>
                                " . (isset($data['balance_remaining']) && $data['balance_remaining'] > 0 ? '⏳ PARTIAL PAYMENT RECEIVED' : '✅ PAYMENT COMPLETED') . "
                            </p>
                        </div>

                        " . (isset($data['balance_remaining']) && $data['balance_remaining'] > 0 && isset($data['payment_link']) ? "
                        <!-- Complete Payment Section -->
                        <div class='payment-section' style='text-align: center; margin: 20px 0; padding: 20px; background: #fef3c7; border-radius: 8px; border: 2px solid #f59e0b;'>
                            <h4 style='color: #92400e; margin-top: 0;'>Complete Your Booking</h4>
                            <p style='margin: 10px 0; color: #92400e;'>You have a remaining balance to complete your booking.</p>
                            <a href='{$data['payment_link']}' class='payment-button' style='display: inline-block; background: linear-gradient(135deg, #f59e0b, #ea580c); color: white !important; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; margin: 10px 0;'>
                                Complete Payment
                            </a>
                            <p style='font-size: 12px; color: #92400e; margin-top: 10px;'>
                                Secure payment processing • Multiple payment options available
                            </p>
                        </div>
                        " : "") . "
                    </div>

                    <div style='background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #f59e0b;'>
                        <h3 style='color: #f59e0b; margin-top: 0;'>What's Next?</h3>
                        <ul style='color: #92400e; margin: 0; padding-left: 20px;'>
                            <li>Our team will contact you to confirm final details</li>
                            <li>You'll receive a detailed itinerary before your trip</li>
                            <li>We'll provide emergency contact numbers and travel tips</li>
                            <li>Get ready for an amazing safari adventure!</li>
                        </ul>
                    </div>

                    <div class='contact-info'>
                        <h3 style='color: #10b981; margin-top: 0;'>📞 Need Assistance?</h3>
                        <p>Our team is here to help with any questions about your booking:</p>
                        <p><strong>Phone:</strong> <a href='tel:{$contactInfo['phone_number']}'>{$contactInfo['phone_number']}</a></p>
                        <p><strong>Email:</strong> <a href='mailto:" . ($contactInfo['booking_email'] ?? '<EMAIL>') . "'>" . ($contactInfo['booking_email'] ?? '<EMAIL>') . "</a></p>
                        <p><strong>Hours:</strong> {$contactInfo['working_hours']}</p>
                        <p style='font-size: 12px; color: #6b7280;'>Simply reply to this email and we'll get back to you promptly.</p>
                    </div>

                    <p><strong>Important:</strong> Please keep this receipt for your records. You may need it for travel insurance or visa applications.</p>

                    <p>We're excited to welcome you to Kenya and show you the wonders of our beautiful country!</p>

                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='" . $this->getBaseUrl() . "/' style='display: inline-block; background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>Visit Our Website</a>
                        <a href='" . $this->getBaseUrl() . "/tours.php' style='display: inline-block; background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>View Tours</a>
                        <a href='" . $this->getBaseUrl() . "/contact.php' style='display: inline-block; background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>Contact Us</a>
                    </div>

                    <div class='signature'>
                        <p><strong>Best regards,</strong><br>
                        Meleva Tours and Travel<br>
                        <em><small>Smooth Travels, Seamless Experiences!</small></em></p>
                    </div>
                </div>
                <div class='footer'>
                    <p>You can reply directly to this email for further assistance.</p>
                    <p>This is an automated receipt. Please keep it for your records.</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Get payment receipt plain text template
     */
    private function getPaymentReceiptPlainText($data) {
        $contactInfo = $this->contactModel->getCurrent();

        return "MELEVA TOURS AND TRAVEL - Payment Receipt\n\n" .
               "PAYMENT RECEIVED!\n\n" .
               "Dear {$data['customer_name']},\n\n" .
               "Thank you for your payment! We have successfully received your payment for your travel booking. Your adventure with Meleva Tours is now confirmed!\n\n" .
               "==========================================\n" .
               "🧾 PAYMENT RECEIPT\n" .
               "==========================================\n\n" .
               "CUSTOMER & TRANSACTION INFO:\n" .
               "Customer: {$data['customer_name']}\n" .
               "Quote Reference: {$data['quote_reference']}\n" .
               "Payment Reference: {$data['payment_reference']}\n" .
               "Transaction Date: " . date('F j, Y') . "\n" .
               "Transaction Time: " . date('g:i A') . "\n" .
               "Payment Method: " . ($data['payment_method'] ?? 'Online Payment') . "\n\n" .
               "BOOKING DETAILS:\n" .
               "Travel Date: " . ($data['travel_date'] ? date('F j, Y', strtotime($data['travel_date'])) : 'To be determined') . "\n" .
               "Travelers: {$data['number_of_adults']} Adult(s)" . ($data['number_of_children'] > 0 ? ", {$data['number_of_children']} Child(ren)" : "") . "\n" .
               "Payment Method: " . ($data['payment_method'] ?? 'Online Payment') . "\n\n" .
               "💰 PAYMENT SUMMARY:\n" .
               "Quote Total: $" . number_format($data['quoted_amount'] ?? 0, 2) . " USD\n" .
               "Amount Paid (This Transaction): $" . number_format($data['amount_paid'], 2) . " USD\n" .
               (isset($data['total_paid']) ? "Total Amount Paid: $" . number_format($data['total_paid'], 2) . " USD\n" : "") .
               (isset($data['balance_remaining']) && $data['balance_remaining'] > 0 ?
                   "Balance Remaining: $" . number_format($data['balance_remaining'], 2) . " USD\n\n" .
                   "⏳ PAYMENT STATUS: PARTIAL PAYMENT RECEIVED\n" :
                   "\n✅ PAYMENT STATUS: PAYMENT COMPLETED\n") . "\n" .
               (isset($data['balance_remaining']) && $data['balance_remaining'] > 0 && isset($data['payment_link']) ?
                   "COMPLETE YOUR BOOKING:\n" .
                   "You have a remaining balance to complete your booking.\n" .
                   "Complete Payment: " . $data['payment_link'] . "\n" .
                   "Secure payment processing • Multiple payment options available\n\n" : "") .
               "WHAT'S NEXT?\n" .
               "- Our team will contact you to confirm final details\n" .
               "- You'll receive a detailed itinerary before your trip\n" .
               "- We'll provide emergency contact numbers and travel tips\n" .
               "- Get ready for an amazing safari adventure!\n\n" .
               "NEED ASSISTANCE?\n" .
               "Our team is here to help with any questions about your booking:\n" .
               "Phone: {$contactInfo['phone_number']}\n" .
               "Email: " . ($contactInfo['booking_email'] ?? '<EMAIL>') . "\n" .
               "Hours: {$contactInfo['working_hours']}\n" .
               "Simply reply to this email and we'll get back to you promptly.\n\n" .
               "IMPORTANT: Please keep this receipt for your records. You may need it for travel insurance or visa applications.\n\n" .
               "We're excited to welcome you to Kenya and show you the wonders of our beautiful country!\n\n" .
               "VISIT OUR WEBSITE | VIEW TOURS | CONTACT US\n" .
               "Website: " . $this->getBaseUrl() . "/\n" .
               "View Tours: " . $this->getBaseUrl() . "/tours.php\n" .
               "Contact Us: " . $this->getBaseUrl() . "/contact.php\n\n" .
               "Best regards,\n" .
               "Meleva Tours and Travel\n" .
               "Smooth Travels, Seamless Experiences!\n\n" .
               "You can reply directly to this email for further assistance.\n" .
               "This is an automated receipt. Please keep it for your records.";
    }

    /**
     * Get quote change notification HTML template
     */
    private function getQuoteChangeEmailTemplate($quoteData, $changeData, $adminUser = null) {
        $contactInfo = $this->contactModel->getCurrent();
        $primaryColor = '#dc2626';
        $paymentUrl = $this->getBaseUrl() . "/payment.php?quote_ref=" . urlencode($quoteData['quote_reference']);

        // Calculate balance to pay
        $totalPaid = floatval($quoteData['total_paid'] ?? 0);
        $newAmount = floatval($changeData['new_amount']);
        $balanceToPay = $newAmount - $totalPaid;

        return "
        <html>
        <head>
            <title>Quote Updated - {$quoteData['quote_reference']} | Meleva Tours</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; background: #ffffff; }
                .header { background: linear-gradient(135deg, #dc2626, #ea580c); color: white; padding: 25px; text-align: center; border-radius: 8px 8px 0 0; }
                .content { background: #f8fafc; padding: 25px; border-radius: 0 0 8px 8px; border: 1px solid #e2e8f0; }
                .highlight { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #dc2626; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .change-details { background: #fef3c7; padding: 20px; border-radius: 8px; border-left: 4px solid #f59e0b; margin: 20px 0; }
                .contact-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e2e8f0; }
                .payment-section { background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #f59e0b; text-align: center; }
                .payment-button { display: inline-block; background: linear-gradient(135deg, #f59e0b, #ea580c); color: white !important; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; margin: 10px 0; transition: all 0.3s ease; }
                .amount-change { font-size: 18px; font-weight: bold; }
                " . $this->getCommonEmailStyles() . "
                .old-amount { text-decoration: line-through; color: #ef4444; }
                .new-amount { color: #059669; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <div class='logo'>
                        <img src='" . $this->getLogoBase64() . "' alt='Meleva Tours and Travel'>
                    </div>
                    <h2>Quote Updated</h2>
                </div>
                <div class='content'>
                    <p>Dear <strong>{$quoteData['customer_name']}</strong>,</p>

                    <p>We have updated your travel quote. Please review the changes below:</p>

                    <div class='highlight'>
                        <h3 style='color: {$primaryColor}; margin-top: 0;'>Quote Reference: {$quoteData['quote_reference']}</h3>
                        <p><strong>Travel Date:</strong> " . ($quoteData['travel_date'] ? date('F j, Y', strtotime($quoteData['travel_date'])) : 'To be determined') . "</p>
                        <p><strong>Travelers:</strong> {$quoteData['number_of_adults']} Adult(s)" . ($quoteData['number_of_children'] > 0 ? ", {$quoteData['number_of_children']} Child(ren)" : "") . "</p>
                    </div>

                    <div class='change-details'>
                        <h3 style='color: #f59e0b; margin-top: 0;'>Quote Amount Updated</h3>
                        <div class='amount-change'>
                            <p>Previous Amount: <span class='old-amount'>$" . number_format($changeData['previous_amount'], 2) . " USD</span></p>
                            <p>New Amount: <span class='new-amount'>$" . number_format($changeData['new_amount'], 2) . " USD</span></p>
                            " . ($totalPaid > 0 ? "<p>Amount Already Paid: <span class='paid-amount'>$" . number_format($totalPaid, 2) . " USD</span></p>" : "") . "
                            " . ($totalPaid > 0 ? "<p><strong>Balance to Pay: <span class='balance-amount' style='color: #dc2626;'>$" . number_format($balanceToPay, 2) . " USD</span></strong></p>" : "") . "
                        </div>
                        " . (!empty($changeData['reason']) ? "<p><strong>Reason for Change:</strong><br>" . nl2br(htmlspecialchars($changeData['reason'])) . "</p>" : "") . "
                    </div>

                    <p>If you have any questions about this change or would like to proceed with booking, please don't hesitate to contact us.</p>

                    <div class='contact-info'>
                        <h3 style='color: {$primaryColor}; margin-top: 0;'>Contact Information</h3>
                        <p><strong>Phone:</strong> {$contactInfo['phone_number']}</p>
                        <p><strong>Email:</strong> " . ($contactInfo['booking_email'] ?? '<EMAIL>') . "</p>
                        <p><strong>Hours:</strong> {$contactInfo['working_hours']}</p>
                    </div>

                    <div class='payment-section'>
                        <h3 style='color: #92400e; margin-top: 0;'>Ready to Secure Your Safari?</h3>
                        <p>Your updated quote is ready for payment. Click below to proceed with your booking:</p>
                        <a href='{$paymentUrl}' class='payment-button' style='color: white !important;'>
                            Proceed to Payment
                        </a>
                        <p style='font-size: 12px; color: #92400e; margin-top: 10px;'>
                            Secure payment processing • Multiple payment options available
                        </p>
                    </div>

                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='" . $this->getBaseUrl() . "/' style='display: inline-block; background: {$primaryColor}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>Visit Our Website</a>
                        <a href='" . $this->getBaseUrl() . "/tours.php' style='display: inline-block; background: {$primaryColor}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>View Tours</a>
                        <a href='" . $this->getBaseUrl() . "/contact.php' style='display: inline-block; background: {$primaryColor}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>Contact Us</a>
                    </div>

                    <div class='signature'>
                        <p><strong>Best regards,</strong><br>
                        <span style='color: {$primaryColor}; font-weight: bold;'>" . ($adminUser ? htmlspecialchars($adminUser['full_name'] ?? $adminUser['username']) : 'Meleva Tours Team') . "</span><br>
                        Meleva Tours and Travel<br>
                        <em><small>Smooth Travels, Seamless Experiences!</small></em></p>
                    </div>
                </div>
                <div class='footer'>
                    <p>You can reply directly to this email for further assistance.</p>
                    <p>This quote update was prepared specifically for your travel requirements.</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Get quote change notification plain text version
     */
    private function getQuoteChangeEmailPlainText($quoteData, $changeData, $adminUser = null) {
        $contactInfo = $this->contactModel->getCurrent();

        // Calculate balance to pay
        $totalPaid = floatval($quoteData['total_paid'] ?? 0);
        $newAmount = floatval($changeData['new_amount']);
        $balanceToPay = $newAmount - $totalPaid;

        return "MELEVA TOURS AND TRAVEL - Quote Updated\n\n" .
               "Dear {$quoteData['customer_name']},\n\n" .
               "We have updated your travel quote. Please review the changes below:\n\n" .
               "QUOTE DETAILS:\n" .
               "Reference: {$quoteData['quote_reference']}\n" .
               "Travel Date: " . ($quoteData['travel_date'] ? date('F j, Y', strtotime($quoteData['travel_date'])) : 'To be determined') . "\n" .
               "Travelers: {$quoteData['number_of_adults']} Adult(s)" . ($quoteData['number_of_children'] > 0 ? ", {$quoteData['number_of_children']} Child(ren)" : "") . "\n\n" .
               "QUOTE AMOUNT UPDATED:\n" .
               "Previous Amount: $" . number_format($changeData['previous_amount'], 2) . " USD\n" .
               "New Amount: $" . number_format($changeData['new_amount'], 2) . " USD\n" .
               ($totalPaid > 0 ? "Amount Already Paid: $" . number_format($totalPaid, 2) . " USD\n" : "") .
               ($totalPaid > 0 ? "BALANCE TO PAY: $" . number_format($balanceToPay, 2) . " USD\n" : "") .
               (!empty($changeData['reason']) ? "Reason for Change: " . $changeData['reason'] . "\n" : "") . "\n" .
               "If you have any questions about this change or would like to proceed with booking, please contact us:\n\n" .
               "CONTACT INFORMATION:\n" .
               "Phone: {$contactInfo['phone_number']}\n" .
               "Email: " . ($contactInfo['booking_email'] ?? '<EMAIL>') . "\n" .
               "Hours: {$contactInfo['working_hours']}\n\n" .
               "READY TO SECURE YOUR SAFARI?\n" .
               "Your updated quote is ready for payment. Click below to proceed with your booking:\n\n" .
               "PROCEED TO PAYMENT\n" .
               $this->getBaseUrl() . "/payment.php?quote_ref=" . urlencode($quoteData['quote_reference']) . "\n\n" .
               "Secure payment processing • Multiple payment options available\n\n" .
               "VISIT OUR WEBSITE | VIEW TOURS | CONTACT US\n" .
               "Website: " . $this->getBaseUrl() . "/\n" .
               "View Tours: " . $this->getBaseUrl() . "/tours.php\n" .
               "Contact Us: " . $this->getBaseUrl() . "/contact.php\n\n" .
               "Best regards,\n" .
               ($adminUser ? ($adminUser['full_name'] ?? $adminUser['username']) : 'Meleva Tours Team') . "\n" .
               "Meleva Tours and Travel\n" .
               "Smooth Travels, Seamless Experiences!\n\n" .
               "You can reply directly to this email for further assistance.\n" .
               "This quote update was prepared specifically for your travel requirements.";
    }


}
