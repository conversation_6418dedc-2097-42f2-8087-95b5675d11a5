-- <PERSON>eva Tours Admin Dashboard Database Schema
-- MySQL Database Creation Script

CREATE DATABASE IF NOT EXISTS jaslanen_meleva_tours;

USE jaslanen_meleva_tours;

-- Users table for admin authentication
CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL,
    role ENUM('admin', 'super_admin') DEFAULT 'admin',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY username (username),
    UNIQUE KEY email (email)
);

-- Password Reset Tokens table
CREATE TABLE password_resets (
    reset_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE KEY token (token),
    INDEX idx_expires (expires_at),
    INDEX idx_user_id (user_id)
);

-- Gallery table (referenced by images)
CREATE TABLE gallery (
    gallery_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY name (name)
);

-- Tour Package Types table
CREATE TABLE tour_package_types (
    package_type_id INT AUTO_INCREMENT PRIMARY KEY,
    type_name VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY type_name (type_name)
);

-- Images table (moved before destinations to resolve foreign key dependencies)
CREATE TABLE images (
    image_id INT AUTO_INCREMENT PRIMARY KEY,
    url VARCHAR(255) NOT NULL,
    alt_text VARCHAR(255),
    is_display_image BOOLEAN DEFAULT FALSE,
    image_category ENUM('main_gallery', 'destinations', 'tour_packages') NOT NULL DEFAULT 'destinations',
    destination_id INT NULL,
    tour_package_id INT NULL,
    gallery_id INT NULL,
    uploaded_by_user_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY url (url),
    KEY destination_id (destination_id),
    KEY tour_package_id (tour_package_id),
    KEY gallery_id (gallery_id),
    KEY uploaded_by_user_id (uploaded_by_user_id),
    FOREIGN KEY (gallery_id) REFERENCES gallery(gallery_id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by_user_id) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Destinations table
CREATE TABLE destinations (
    destination_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    location VARCHAR(100),
    price DECIMAL(10, 2) NOT NULL,
    short_description TEXT,
    full_description TEXT,
    display_image_id INT,
    created_by_user_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY name (name),
    KEY created_by_user_id (created_by_user_id),
    KEY fk_destination_display_image (display_image_id),
    FOREIGN KEY (created_by_user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (display_image_id) REFERENCES images(image_id) ON DELETE SET NULL
);

-- Tour Packages table
CREATE TABLE tour_packages (
    tour_package_id INT AUTO_INCREMENT PRIMARY KEY,
    package_type_id INT,
    destination_id INT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    itinerary TEXT,
    price DECIMAL(10, 2) NOT NULL,
    duration VARCHAR(50),
    is_featured BOOLEAN DEFAULT FALSE,
    display_image_id INT,
    created_by_user_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    KEY package_type_id (package_type_id),
    KEY created_by_user_id (created_by_user_id),
    KEY fk_package_display_image (display_image_id),
    KEY idx_tour_packages_featured (is_featured),
    KEY fk_tour_packages_destination (destination_id),
    FOREIGN KEY (package_type_id) REFERENCES tour_package_types(package_type_id) ON DELETE SET NULL,
    FOREIGN KEY (destination_id) REFERENCES destinations(destination_id) ON DELETE SET NULL,
    FOREIGN KEY (created_by_user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (display_image_id) REFERENCES images(image_id) ON DELETE SET NULL
);



-- Contact Info table
CREATE TABLE contact_info (
    contact_id INT AUTO_INCREMENT PRIMARY KEY,
    phone_number VARCHAR(20),
    email VARCHAR(100),
    booking_email VARCHAR(100) DEFAULT '<EMAIL>',
    address TEXT,
    working_hours VARCHAR(100),
    map_embed_code TEXT,
    facebook_url VARCHAR(255),
    twitter_url VARCHAR(255),
    instagram_url VARCHAR(255),
    tiktok_url VARCHAR(255),
    youtube_url VARCHAR(255),
    last_edited_by_user_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    KEY last_edited_by_user_id (last_edited_by_user_id),
    FOREIGN KEY (last_edited_by_user_id) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Messages table for contact form submissions with email tracking
CREATE TABLE messages (
    message_id INT AUTO_INCREMENT PRIMARY KEY,
    conversation_id VARCHAR(50) NULL,
    email_message_id VARCHAR(255) NULL,
    in_reply_to VARCHAR(255) NULL,
    email_thread_id VARCHAR(255) NULL,
    message_type ENUM('incoming', 'outgoing') DEFAULT 'incoming',
    email_status ENUM('sent', 'delivered', 'read', 'failed') NULL,
    original_message_id INT NULL,
    sender_name VARCHAR(100) NOT NULL,
    sender_email VARCHAR(100) NOT NULL,
    subject VARCHAR(200),
    message_content TEXT NOT NULL,
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_read BOOLEAN DEFAULT FALSE,
    replied_at TIMESTAMP NULL,
    reply_content TEXT NULL,
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_email_message_id (email_message_id),
    INDEX idx_email_thread_id (email_thread_id),
    FOREIGN KEY (original_message_id) REFERENCES messages(message_id) ON DELETE SET NULL
);

-- Email conversations table for better conversation management
CREATE TABLE email_conversations (
    conversation_id VARCHAR(50) PRIMARY KEY,
    initial_message_id INT NOT NULL,
    sender_email VARCHAR(100) NOT NULL,
    sender_name VARCHAR(100) NOT NULL,
    subject VARCHAR(200) NOT NULL,
    status ENUM('open', 'closed', 'pending') DEFAULT 'open',
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (initial_message_id) REFERENCES messages(message_id) ON DELETE CASCADE,
    INDEX idx_sender_email (sender_email),
    INDEX idx_status (status),
    INDEX idx_last_activity (last_activity)
);

-- Email tracking table for detailed email delivery tracking
CREATE TABLE email_tracking (
    tracking_id INT AUTO_INCREMENT PRIMARY KEY,
    message_id INT NOT NULL,
    email_message_id VARCHAR(255) NOT NULL,
    email_provider VARCHAR(50) NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    delivered_at TIMESTAMP NULL,
    opened_at TIMESTAMP NULL,
    clicked_at TIMESTAMP NULL,
    bounced_at TIMESTAMP NULL,
    status ENUM('queued', 'sent', 'delivered', 'opened', 'clicked', 'bounced', 'failed') DEFAULT 'queued',
    error_message TEXT NULL,
    FOREIGN KEY (message_id) REFERENCES messages(message_id) ON DELETE CASCADE,
    INDEX idx_email_message_id (email_message_id),
    INDEX idx_status (status)
);

-- Reports table
CREATE TABLE reports (
    report_id INT AUTO_INCREMENT PRIMARY KEY,
    report_name VARCHAR(100) NOT NULL,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    report_data JSON,
    generated_by_user_id INT,
    KEY generated_by_user_id (generated_by_user_id),
    FOREIGN KEY (generated_by_user_id) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Quotes table for quote requests and management
CREATE TABLE quotes (
    quote_id INT AUTO_INCREMENT PRIMARY KEY,
    quote_reference VARCHAR(20) NOT NULL,
    customer_name VARCHAR(100) NOT NULL,
    customer_email VARCHAR(100) NOT NULL,
    customer_phone VARCHAR(20) NOT NULL,
    customer_country VARCHAR(50),
    travel_date DATE,
    number_of_adults INT DEFAULT 1,
    number_of_children INT DEFAULT 0,
    special_requirements TEXT,
    quote_status ENUM('pending', 'quoted', 'accepted', 'rejected', 'expired', 'paid') DEFAULT 'pending',
    quoted_amount DECIMAL(10, 2) NULL,
    quoted_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY quote_reference (quote_reference),
    INDEX idx_quote_status (quote_status),
    INDEX idx_customer_email (customer_email),
    INDEX idx_travel_date (travel_date)
);

-- Quote packages junction table (many-to-many relationship)
CREATE TABLE quote_packages (
    quote_package_id INT AUTO_INCREMENT PRIMARY KEY,
    quote_id INT NOT NULL,
    tour_package_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (quote_id) REFERENCES quotes(quote_id) ON DELETE CASCADE,
    FOREIGN KEY (tour_package_id) REFERENCES tour_packages(tour_package_id) ON DELETE CASCADE,
    UNIQUE KEY unique_quote_package (quote_id, tour_package_id),
    INDEX idx_quote_id (quote_id),
    INDEX idx_tour_package_id (tour_package_id)
);

-- Bookings table for confirmed bookings
CREATE TABLE bookings (
    booking_id INT AUTO_INCREMENT PRIMARY KEY,
    booking_reference VARCHAR(20) NOT NULL,
    quote_id INT NULL,
    customer_name VARCHAR(100) NOT NULL,
    customer_email VARCHAR(100) NOT NULL,
    customer_phone VARCHAR(20) NOT NULL,
    customer_country VARCHAR(50),
    travel_date DATE NOT NULL,
    number_of_adults INT DEFAULT 1,
    number_of_children INT DEFAULT 0,
    total_amount DECIMAL(10, 2) NOT NULL,
    deposit_amount DECIMAL(10, 2) NOT NULL,
    balance_amount DECIMAL(10, 2) NOT NULL,
    special_requirements TEXT,
    booking_status ENUM('pending', 'confirmed', 'paid', 'cancelled', 'completed') DEFAULT 'pending',
    payment_status ENUM('pending', 'partial', 'paid', 'refunded') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (quote_id) REFERENCES quotes(quote_id) ON DELETE SET NULL,
    UNIQUE KEY booking_reference (booking_reference),
    INDEX idx_booking_status (booking_status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_customer_email (customer_email),
    INDEX idx_travel_date (travel_date),
    INDEX idx_quote_id (quote_id)
);

-- Booking packages junction table (many-to-many relationship)
CREATE TABLE booking_packages (
    booking_package_id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    tour_package_id INT NOT NULL,
    package_price DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE,
    FOREIGN KEY (tour_package_id) REFERENCES tour_packages(tour_package_id) ON DELETE CASCADE,
    UNIQUE KEY unique_booking_package (booking_id, tour_package_id),
    INDEX idx_booking_id (booking_id),
    INDEX idx_tour_package_id (tour_package_id)
);

-- Payments table for tracking all payment transactions
CREATE TABLE payments (
    payment_id INT AUTO_INCREMENT PRIMARY KEY,
    payment_reference VARCHAR(50) NOT NULL,
    booking_id INT NULL,
    quote_id INT NULL,
    pesapal_tracking_id VARCHAR(100) NULL,
    pesapal_merchant_reference VARCHAR(100) NULL,
    payment_method VARCHAR(50) NULL,
    payment_type ENUM('deposit', 'balance', 'full') NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    payment_status ENUM('pending', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
    pesapal_status VARCHAR(50) NULL,
    payment_date TIMESTAMP NULL,
    pesapal_response JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE,
    FOREIGN KEY (quote_id) REFERENCES quotes(quote_id) ON DELETE CASCADE,
    UNIQUE KEY payment_reference (payment_reference),
    INDEX idx_payment_status (payment_status),
    INDEX idx_booking_id (booking_id),
    INDEX idx_quote_id (quote_id),
    INDEX idx_pesapal_tracking_id (pesapal_tracking_id),
    INDEX idx_payment_date (payment_date)
);



-- Insert default admin user (password: Admin@123)
INSERT INTO users (username, password_hash, email, role) VALUES
('admin', '$2y$10$/90/FcRNhjvl0GcEN9pnnulqZE9mhmnZGvzBRX/N.mNgg6ffXPOeS', '<EMAIL>', 'super_admin');

-- Insert default contact info
INSERT INTO contact_info (phone_number, email, booking_email, address, working_hours, last_edited_by_user_id) VALUES
('+254712345678', '<EMAIL>', '<EMAIL>', '123 Mavueni, Kilifi, Kenya', 'Mon-Fri: 9AM-6PM, Sat: 9AM-2PM', 1);

-- Insert default gallery
INSERT INTO gallery (name, description) VALUES
('Main Gallery', 'Main website gallery showcasing safari adventures'),
('Destination Gallery', 'Images of various travel destinations'),
('Tour Package Gallery', 'Images for tour packages');

-- Insert default tour package types
INSERT INTO tour_package_types (type_name, description) VALUES
('Day Tours', 'Daily adventures'),
('Group Tours', 'Explore in groups'),
('Private Tours', 'Extreme sports and adventure activities'),
('Luxury Tours', 'Premium and luxury travel experiences');

-- Initialize conversation IDs for existing messages (if any)
-- This will be handled by the application when messages are created

-- Add foreign key constraints for images table (after all tables are created)
ALTER TABLE images
ADD FOREIGN KEY (destination_id) REFERENCES destinations(destination_id) ON DELETE CASCADE,
ADD FOREIGN KEY (tour_package_id) REFERENCES tour_packages(tour_package_id) ON DELETE CASCADE;

-- Note: Social media fields can be added to contact_info table using the migration script:
-- admin-dashboard/migrations/add_social_media_fields.sql
-- Email tracking system is now included in the base schema


