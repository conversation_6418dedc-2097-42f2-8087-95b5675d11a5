<!DOCTYPE html>
<html>
<head>
    <title>Test AJAX Message Update</title>
</head>
<body>
    <h1>Test AJAX Message Update</h1>
    
    <button onclick="testMarkUnread()">Mark Message 104 as Unread</button>
    <button onclick="testMarkRead()">Mark Message 104 as Read</button>
    
    <div id="result"></div>

    <script>
        function testMarkUnread() {
            testAPI('mark_unread');
        }
        
        function testMarkRead() {
            testAPI('mark_read');
        }
        
        function testAPI(action) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing ' + action + '...';
            
            fetch('api/test_update_message_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message_id: 104,
                    action: action
                })
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                resultDiv.innerHTML = 'Error: ' + error.message;
                console.error('Error:', error);
            });
        }
    </script>
</body>
</html>
