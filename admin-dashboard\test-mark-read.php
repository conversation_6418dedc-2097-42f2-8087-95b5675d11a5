<?php
require_once 'config/config.php';

$db = Database::getInstance()->getConnection();

echo "=== Marking Message 104 as Read ===\n";

$stmt = $db->prepare('UPDATE messages SET is_read = 1 WHERE message_id = 104');
$result = $stmt->execute();

if ($result) {
    echo "✅ Message 104 marked as read\n";
} else {
    echo "❌ Failed to mark message as read\n";
}

// Check current status
$stmt = $db->prepare('SELECT message_id, sender_name, is_read FROM messages WHERE message_id = 104');
$stmt->execute();
$message = $stmt->fetch();

if ($message) {
    echo "Current status: " . ($message['is_read'] ? 'READ' : 'UNREAD') . "\n";
} else {
    echo "Message not found\n";
}
?>
