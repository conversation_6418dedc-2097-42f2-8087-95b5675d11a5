<?php
require_once 'config/config.php';

$db = Database::getInstance()->getConnection();

echo "=== Simple Database Check ===\n";

// Count total messages
$stmt = $db->query("SELECT COUNT(*) as count FROM messages");
$total = $stmt->fetch();
echo "Total messages: " . $total['count'] . "\n";

// Count unread messages
$stmt = $db->query("SELECT COUNT(*) as count FROM messages WHERE is_read = 0");
$unread = $stmt->fetch();
echo "Unread messages: " . $unread['count'] . "\n";

// Count incoming messages
$stmt = $db->query("SELECT COUNT(*) as count FROM messages WHERE message_type = 'incoming' OR message_type IS NULL");
$incoming = $stmt->fetch();
echo "Incoming messages: " . $incoming['count'] . "\n";

// Get the latest message
$stmt = $db->query("SELECT * FROM messages ORDER BY received_at DESC LIMIT 1");
$latest = $stmt->fetch();
if ($latest) {
    echo "Latest message: ID " . $latest['message_id'] . " from " . $latest['sender_name'] . " at " . $latest['received_at'] . "\n";
    echo "Subject: " . $latest['subject'] . "\n";
    echo "Type: " . ($latest['message_type'] ?? 'NULL') . "\n";
    echo "Read: " . ($latest['is_read'] ? 'Yes' : 'No') . "\n";
}

echo "\n=== Done ===\n";
?>
