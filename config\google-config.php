<?php
/**
 * Google Business Profile Configuration
 *
 * To set up Google Reviews integration:
 *
 * 1. Get Google Places API Key:
 *    - Go to https://console.cloud.google.com/
 *    - Create a new project or select existing one
 *    - Enable "Places API"
 *    - Create credentials (API Key)
 *    - Restrict the API key to "Places API" for security
 *
 * 2. Find your Google Business Place ID:
 *    - Go to https://developers.google.com/maps/documentation/places/web-service/place-id
 *    - Use the Place ID Finder tool
 *    - Search for "Meleva Tours and Travel" or your business address
 *    - Copy the Place ID (starts with something like "ChIJ...")
 *
 * 3. Update the values below with your actual credentials
 */

// Prevent multiple inclusions
if (!defined('GOOGLE_CONFIG_LOADED')) {
    define('GOOGLE_CONFIG_LOADED', true);

    // Google Places API Configuration
    define('GOOGLE_PLACES_API_KEY', 'YOUR_GOOGLE_API_KEY_HERE');
    define('GOOGLE_BUSINESS_PLACE_ID', 'YOUR_PLACE_ID_HERE');

    // Cache settings
    define('GOOGLE_REVIEWS_CACHE_DURATION', 3600); // 1 hour in seconds

    // Review display settings
    define('MAX_REVIEWS_DISPLAY', 3);
    define('MIN_REVIEW_RATING', 4); // Only show reviews with 4+ stars
    define('MAX_REVIEW_TEXT_LENGTH', 150); // Truncate long reviews

    // Manual review selection mode
    define('USE_MANUAL_REVIEWS', true); // Set to false to use Google API
    define('USE_MIXED_REVIEWS', false); // Set to true to mix manual + Google reviews
}

/**
 * Example values (replace with your actual values):
 * 
 * GOOGLE_PLACES_API_KEY: 'AIzaSyBvOkBjvPqHexAMPle...'
 * GOOGLE_BUSINESS_PLACE_ID: 'ChIJN1t_tDeuEmsRUsoyG83frY4'
 */

// MANUALLY CURATED REVIEWS
// Edit these reviews to display exactly what you want on your homepage
$manual_reviews = array (
  0 => 
  array (
    'name' => 'Eva K.',
    'rating' => 5,
    'text' => 'The safari experience was absolutely amazing! The guides were incredibly knowledgeable and made every moment unforgettable. Meleva Tours exceeded all our expectations.',
    'date' => 'June 2024',
    'avatar' => NULL,
    'source' => 'Google',
    'location' => '',
  ),
  1 => 
  array (
    'name' => 'Sarah M.',
    'rating' => 5,
    'text' => 'Diani Beach was absolute paradise, and the planning was seamless from start to finish. I highly recommend Meleva Tours for anyone seeking authentic African experiences!',
    'date' => 'May 2024',
    'avatar' => NULL,
    'source' => 'Google',
    'location' => '',
  ),
  2 => 
  array (
    'name' => 'James L.',
    'rating' => 5,
    'text' => 'Mount Kenya trek was challenging but incredibly rewarding. The team provided excellent support throughout the journey. An adventure I&#039;ll never forget!',
    'date' => 'April 2024',
    'avatar' => NULL,
    'source' => 'Google',
    'location' => '',
  ),
);

// Fallback reviews (used when API is unavailable and manual mode is off)
$fallback_reviews = [
    [
        'name' => 'Eva K.',
        'rating' => 5,
        'text' => 'The safari experience was absolutely amazing! The guides were incredibly knowledgeable and made every moment unforgettable. Meleva Tours exceeded all our expectations.',
        'date' => 'June 2024',
        'avatar' => null,
        'source' => 'Google'
    ],
    [
        'name' => 'Sarah M.',
        'rating' => 5,
        'text' => 'Diani Beach was absolute paradise, and the planning was seamless from start to finish. I highly recommend Meleva Tours for anyone seeking authentic African experiences!',
        'date' => 'May 2024',
        'avatar' => null,
        'source' => 'Google'
    ],
    [
        'name' => 'James L.',
        'rating' => 5,
        'text' => 'Mount Kenya trek was challenging but incredibly rewarding. The team provided excellent support throughout the journey. An adventure I\'ll never forget!',
        'date' => 'April 2024',
        'avatar' => null,
        'source' => 'Google'
    ],
    [
        'name' => 'Michael R.',
        'rating' => 5,
        'text' => 'Professional service from start to finish. The Maasai Mara experience was breathtaking and the accommodation was top-notch.',
        'date' => 'March 2024',
        'avatar' => null,
        'source' => 'Google'
    ],
    [
        'name' => 'Lisa T.',
        'rating' => 5,
        'text' => 'Exceptional customer service and attention to detail. Our family safari was perfectly organized and created memories that will last a lifetime.',
        'date' => 'February 2024',
        'avatar' => null,
        'source' => 'Google'
    ]
];
