<?php
/**
 * Update existing messages to have correct categories and clean reply content
 */

require_once 'config/config.php';

/**
 * Extract only the reply content, removing quoted original message
 */
function extractReplyContent($emailBody) {
    // Common patterns that indicate quoted content
    $quotedPatterns = [
        '/^On .* wrote:.*$/m',           // "On [date] [person] wrote:"
        '/^From:.*$/m',                  // Email headers in quoted content
        '/^Sent:.*$/m',                  // Outlook style headers
        '/^To:.*$/m',                    // To headers
        '/^Subject:.*$/m',               // Subject headers
        '/^Date:.*$/m',                  // Date headers
        '/^>.*$/m',                      // Lines starting with >
        '/^-----Original Message-----.*$/m', // Outlook original message
        '/^________________________________.*$/m', // Outlook separator
        '/^\s*From:.*\n.*\n.*\n.*$/m',   // Multi-line email headers
    ];
    
    $lines = explode("\n", $emailBody);
    $replyLines = [];
    $foundQuotedContent = false;
    
    foreach ($lines as $line) {
        $line = trim($line);
        
        // Check if this line indicates start of quoted content
        foreach ($quotedPatterns as $pattern) {
            if (preg_match($pattern, $line)) {
                $foundQuotedContent = true;
                break 2; // Break out of both loops
            }
        }
        
        // If we haven't found quoted content yet, this is part of the reply
        if (!$foundQuotedContent && !empty($line)) {
            $replyLines[] = $line;
        }
    }
    
    // Join the reply lines and clean up
    $replyContent = implode("\n", $replyLines);
    $replyContent = trim($replyContent);
    
    // If we couldn't extract a clean reply, return the first paragraph
    if (empty($replyContent) || strlen($replyContent) < 10) {
        $paragraphs = explode("\n\n", $emailBody);
        $replyContent = trim($paragraphs[0]);
    }
    
    return $replyContent;
}

$db = Database::getInstance()->getConnection();

echo "=== Updating Existing Messages ===\n";

// Get all incoming messages that need updating
$stmt = $db->prepare("
    SELECT message_id, sender_email, message_content, message_category 
    FROM messages 
    WHERE message_type = 'incoming' OR message_type IS NULL
    ORDER BY message_id DESC
");
$stmt->execute();
$messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "Found " . count($messages) . " incoming messages to process\n\n";

$updatedCount = 0;
$categoryUpdates = 0;
$contentUpdates = 0;

foreach ($messages as $message) {
    $messageId = $message['message_id'];
    $senderEmail = $message['sender_email'];
    $currentContent = $message['message_content'];
    $currentCategory = $message['message_category'];
    
    $needsUpdate = false;
    $updates = [];
    $params = [];
    
    // Determine correct category based on sender or context
    $correctCategory = 'contact'; // default
    
    // If the message is related to quotes (contains quote reference or from booking context)
    if (strpos($message['message_content'], 'QT20') !== false || 
        strpos($message['message_content'], 'quote') !== false ||
        strpos($message['message_content'], 'booking') !== false ||
        strpos($message['message_content'], 'payment') !== false ||
        strpos($message['message_content'], 'receipt') !== false) {
        $correctCategory = 'quote';
    }
    
    // Update category if needed
    if ($currentCategory !== $correctCategory) {
        $updates[] = "message_category = :category";
        $params[':category'] = $correctCategory;
        $needsUpdate = true;
        $categoryUpdates++;
        echo "Message $messageId: Category {$currentCategory} → {$correctCategory}\n";
    }
    
    // Clean the content if it looks like it has quoted content
    if (strpos($currentContent, 'From:') !== false || 
        strpos($currentContent, 'On ') !== false || 
        strpos($currentContent, '>') !== false ||
        strpos($currentContent, '-----Original Message-----') !== false) {
        
        $cleanedContent = extractReplyContent($currentContent);
        
        // Only update if the cleaned content is significantly different and not empty
        if (!empty($cleanedContent) && $cleanedContent !== $currentContent && strlen($cleanedContent) > 10) {
            $updates[] = "message_content = :content";
            $params[':content'] = $cleanedContent;
            $needsUpdate = true;
            $contentUpdates++;
            
            echo "Message $messageId: Content cleaned (";
            echo strlen($currentContent) . " → " . strlen($cleanedContent) . " chars)\n";
            echo "  Original preview: " . substr(str_replace("\n", " ", $currentContent), 0, 100) . "...\n";
            echo "  Cleaned preview:  " . substr(str_replace("\n", " ", $cleanedContent), 0, 100) . "...\n\n";
        }
    }
    
    // Apply updates if needed
    if ($needsUpdate) {
        $sql = "UPDATE messages SET " . implode(', ', $updates) . " WHERE message_id = :message_id";
        $params[':message_id'] = $messageId;
        
        $updateStmt = $db->prepare($sql);
        $updateStmt->execute($params);
        
        $updatedCount++;
    }
}

echo "=== Update Summary ===\n";
echo "Total messages processed: " . count($messages) . "\n";
echo "Messages updated: $updatedCount\n";
echo "Category updates: $categoryUpdates\n";
echo "Content updates: $contentUpdates\n";

// Show current category distribution
echo "\n=== Current Category Distribution ===\n";
$stmt = $db->query("
    SELECT message_category, COUNT(*) as count 
    FROM messages 
    WHERE message_type = 'incoming' OR message_type IS NULL
    GROUP BY message_category
");
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($categories as $cat) {
    $category = $cat['message_category'] ?? 'NULL';
    echo "$category: {$cat['count']} messages\n";
}

echo "\n=== Update Complete ===\n";
?>
