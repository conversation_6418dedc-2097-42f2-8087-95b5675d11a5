<?php
/**
 * Test script for the update_message_status API
 */

// Start session to simulate admin login
session_start();
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_role'] = 'admin';

echo "=== Testing Message Status Update API ===\n";

// Test data
$testData = [
    'message_id' => 104,
    'action' => 'mark_unread'
];

echo "Test data: " . json_encode($testData) . "\n";

// Simulate the API call
$_SERVER['REQUEST_METHOD'] = 'POST';

// Capture the API output
ob_start();

// Set up the input stream
$jsonData = json_encode($testData);
file_put_contents('php://temp', $jsonData);

// Mock the php://input
$GLOBALS['mock_input'] = $jsonData;

// Override file_get_contents for php://input
function file_get_contents_override($filename) {
    if ($filename === 'php://input') {
        return $GLOBALS['mock_input'];
    }
    return file_get_contents($filename);
}

// Include the API file
try {
    // Temporarily override file_get_contents
    $originalInput = $jsonData;
    
    // Create a temporary file with the JSON data
    $tempFile = tempnam(sys_get_temp_dir(), 'api_test');
    file_put_contents($tempFile, $jsonData);
    
    // Test the API logic manually
    require_once 'config/config.php';
    require_once 'classes/models.php';
    
    $messageModel = new Message();
    $messageId = 104;
    
    echo "\n=== Testing Database Update Directly ===\n";
    
    // Check current status
    $message = $messageModel->findById($messageId);
    if ($message) {
        echo "Current status of message $messageId: " . ($message['is_read'] ? 'READ' : 'UNREAD') . "\n";
        
        // Try to mark as unread
        $sql = "UPDATE messages SET is_read = 0 WHERE message_id = :id";
        $stmt = $messageModel->getDb()->prepare($sql);
        $stmt->bindParam(':id', $messageId);
        $success = $stmt->execute();
        
        if ($success) {
            echo "✅ Successfully marked message as unread\n";
            
            // Verify the change
            $updatedMessage = $messageModel->findById($messageId);
            echo "New status: " . ($updatedMessage['is_read'] ? 'READ' : 'UNREAD') . "\n";
            
            // Get stats
            $stats = $messageModel->getStats();
            echo "Total unread messages: " . $stats['unread'] . "\n";
        } else {
            echo "❌ Failed to update message\n";
            $errorInfo = $stmt->errorInfo();
            echo "Error: " . print_r($errorInfo, true) . "\n";
        }
    } else {
        echo "❌ Message not found\n";
    }
    
    // Clean up
    unlink($tempFile);
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== Test Complete ===\n";
?>
