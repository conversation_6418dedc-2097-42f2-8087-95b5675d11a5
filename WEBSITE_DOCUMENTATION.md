# Meleva Tours and Travel - Website Documentation

## Overview
Meleva Tours and Travel is a comprehensive tour and travel website built with PHP, MySQL, and modern web technologies. The website provides a complete solution for tour package management, destination showcasing, customer interaction, and integrated payment processing with Pesapal API 3.0.

## Project Structure

### Frontend Files
```
/
├── index.php              # Homepage with hero section, featured packages, and reviews
├── about.php              # About us page with company information
├── contact.php            # Contact form with IMAP integration
├── tours.php              # Tours listing with tabs (destinations/packages)
├── destination-details.php # Individual destination details with image galleries
├── package-details.php    # Individual package details with booking options
├── gallery.php            # Image gallery with lightbox functionality
├── request-quote.php      # Quote request form with phone validation
├── payment.php            # Payment processing page with Pesapal integration
├── payment-success.php    # Payment success page with balance tracking
├── payment-failed.php     # Payment failure page with retry options
├── payment-status.php     # Payment status checker
├── payment-callback.php   # Pesapal callback handler
├── payment-ipn.php        # Pesapal IPN handler
├── header.php             # Global header with mega navigation
├── footer.php             # Global footer with contact information
├── style.css              # Main stylesheet with Tailwind CSS
├── cookie-policy.php      # Cookie policy page
├── privacy-policy.php     # Privacy policy page
├── terms-of-service.php   # Terms of service page
├── 404.php                # Custom 404 error page
└── js/
    └── global.js          # Global JavaScript with image optimization
```

### Assets
```
/images/                   # Static images
├── hero-bg.jpg           # Homepage hero background
├── meleva-lg.png         # Company logo
├── nav-bg.jpg            # Navigation background
├── favicon.ico           # Website favicon
├── logos/                # Logo variations
└── optimized/            # Optimized image versions

/uploads/                 # Dynamic uploads
└── images/
    ├── destinations/     # Destination images
    ├── packages/         # Package images
    └── main-gallery/     # Gallery images

/cache/                   # Cache directory
└── google_reviews.json   # Google Reviews cache
```

## Key Features

### 1. Homepage (index.php)
- **Hero Section**: 80vh height with compelling call-to-action
- **Featured Packages**: Displays up to 7 featured tour packages
- **Why Choose Us**: Company highlights and benefits
- **Responsive Design**: Mobile-first approach with Tailwind CSS

### 2. Tours Page (tours.php)
- **Tab Navigation**: Separates destinations and tour packages
- **Destination Cards**: 1-line names, 2-line descriptions
- **Package Cards**: 2-line names, 2-line descriptions, "View Details" buttons
- **Responsive Grid**: Adapts to different screen sizes

### 3. Destination Details (destination-details.php)
- **Tab Navigation**: Description and View Packages tabs
- **Image Gallery**: Swiper carousel with navigation
- **Related Packages**: Shows packages for the destination
- **Breadcrumb Navigation**: Clear page hierarchy

### 4. Package Details (package-details.php)
- **Comprehensive Info**: Price, duration, description
- **Image Gallery**: Multiple package images
- **Quote Request**: Direct integration with quote system
- **Pre-filled Forms**: Package selection carries over to quote form

### 5. Gallery (gallery.php)
- **Masonry Layout**: Pinterest-style image grid
- **Hover Effects**: Title and description overlay
- **Empty State**: Graceful handling when no images exist
- **Lazy Loading**: Performance optimization

### 6. Quote System (request-quote.php)
- **Multi-step Form**: Logical grouping of information
- **Package Pre-selection**: From package detail pages
- **Phone Input**: International format with country selection
- **Date Picker**: dd/mm/yyyy format with mobile optimization
- **Email Integration**: Automatic notifications

## Technical Implementation

### Frontend Technologies
- **PHP 7.4+**: Server-side processing
- **MySQL 5.7+**: Database management
- **Tailwind CSS**: Utility-first styling
- **JavaScript ES6+**: Modern client-side functionality
- **Swiper.js**: Touch-enabled carousels
- **Font Awesome**: Icon library

### Design Patterns
- **Component-based**: Reusable header/footer
- **Mobile-first**: Responsive design approach
- **Progressive Enhancement**: Works without JavaScript
- **Semantic HTML**: Accessible markup structure

### Performance Optimizations
- **Image Optimization**: WebP support with fallbacks
- **Lazy Loading**: Intersection Observer API
- **CSS Minification**: Production-ready stylesheets
- **Caching Headers**: Browser caching optimization

### Security Features
- **Input Sanitization**: XSS prevention
- **CSRF Protection**: Form security tokens
- **SQL Injection Prevention**: Prepared statements
- **File Upload Security**: Type and size validation

## Database Integration

### Core Tables
- **destinations**: Location information and images
- **tour_packages**: Package details and pricing
- **tour_package_types**: Package categorization
- **images**: Centralized image management
- **messages**: Contact form submissions
- **quotes**: Quote requests and responses

### Relationships
- Destinations → Images (one-to-many)
- Tour Packages → Images (one-to-many)
- Tour Packages → Destinations (many-to-one)
- Tour Packages → Package Types (many-to-one)

## User Experience Features

### Navigation
- **Sticky Header**: Remains visible on scroll up
- **Mega Menus**: Full-width dropdown navigation
- **Mobile Menu**: Hamburger menu for tablets/phones
- **Active States**: Current page highlighting

### Forms
- **Validation**: Client and server-side validation
- **Error Handling**: User-friendly error messages
- **Success Feedback**: Confirmation messages
- **Auto-save**: Form data persistence

### Responsive Design
- **Breakpoints**: Mobile, tablet, desktop optimization
- **Touch-friendly**: Large tap targets on mobile
- **Readable Typography**: Optimal font sizes and spacing
- **Flexible Layouts**: Adapts to various screen sizes

## Content Management

### Dynamic Content
- All destinations, packages, and images are database-driven
- Admin panel controls all frontend content
- Real-time updates without code changes

### SEO Optimization
- **Meta Tags**: Dynamic title and description
- **Structured Data**: Schema.org markup
- **Clean URLs**: SEO-friendly page structure
- **Image Alt Text**: Accessibility and SEO

## Integration Points

### Payment System
- Pesapal API integration for secure payments
- Multiple payment methods support
- Real-time payment status updates
- Automatic receipt generation

### Email System
- PHPMailer for reliable email delivery
- Template-based email design
- Conversation threading
- Delivery status tracking

## Browser Support
- **Modern Browsers**: Chrome 60+, Firefox 55+, Safari 12+
- **Mobile Browsers**: iOS Safari, Chrome Mobile
- **Graceful Degradation**: Basic functionality in older browsers

## Deployment Requirements
- **PHP**: 7.4 or higher
- **MySQL**: 5.7 or higher
- **Web Server**: Apache with mod_rewrite
- **SSL Certificate**: Required for payment processing
- **File Permissions**: Write access to uploads directory

## Maintenance
- Regular database backups
- Image optimization monitoring
- Security updates
- Performance monitoring
- User feedback integration
