<?php
/**
 * Debug IMAP Fetch - Detailed debugging of email fetching process
 */

require_once 'config/config.php';
require_once 'classes/ImapEmailFetcher.php';

// Check if IMAP extension is loaded
if (!extension_loaded('imap')) {
    die("ERROR: PHP IMAP extension is not installed.\n");
}

echo "=== Debug IMAP Email Fetching ===\n";

// Test the booking account specifically
$connectionString = "{mail.melevatours.co.ke:993/imap/ssl}INBOX";
$email = '<EMAIL>';
$password = '1V^bAvDR!%)6,C&A';

echo "Connecting to: $connectionString\n";
echo "Email: $email\n";

$connection = imap_open($connectionString, $email, $password);

if (!$connection) {
    die("Failed to connect to IMAP server\n");
}

echo "✅ Connected successfully!\n\n";

// Get mailbox status
$status = imap_status($connection, $connectionString, SA_ALL);
echo "=== Mailbox Status ===\n";
echo "Total messages: {$status->messages}\n";
echo "Unread messages: {$status->unseen}\n";
echo "Recent messages: {$status->recent}\n\n";

// Search for unread emails
echo "=== Searching for Unread Emails ===\n";
$unreadEmails = imap_search($connection, 'UNSEEN');

if ($unreadEmails) {
    echo "Found " . count($unreadEmails) . " unread emails:\n\n";
    
    foreach ($unreadEmails as $emailNumber) {
        echo "--- Email #$emailNumber ---\n";
        
        // Get header
        $header = imap_headerinfo($connection, $emailNumber);
        
        if ($header) {
            // Sender info
            $from = $header->from[0] ?? null;
            if ($from) {
                $senderEmail = strtolower($from->mailbox . '@' . $from->host);
                $senderName = isset($from->personal) ? imap_mime_header_decode($from->personal)[0]->text : $senderEmail;
                echo "From: $senderName <$senderEmail>\n";
            }
            
            // Subject
            $subject = isset($header->subject) ? imap_mime_header_decode($header->subject)[0]->text : 'No Subject';
            echo "Subject: $subject\n";
            
            // Date
            echo "Date: " . date('Y-m-d H:i:s', $header->udate) . "\n";
            
            // Message ID
            echo "Message ID: " . ($header->message_id ?? 'None') . "\n";
            
            // Check if this email already exists in database
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("SELECT message_id FROM messages WHERE email_message_id = ? OR (sender_email = ? AND subject = ? AND received_at = ?)");
            $receivedAt = date('Y-m-d H:i:s', $header->udate);
            $stmt->execute([$header->message_id ?? '', $senderEmail, $subject, $receivedAt]);
            $existingMessage = $stmt->fetch();
            
            if ($existingMessage) {
                echo "⚠️  Already exists in database (ID: {$existingMessage['message_id']})\n";
            } else {
                echo "✅ New email - should be processed\n";
                
                // Get body preview
                $body = imap_body($connection, $emailNumber);
                $bodyPreview = substr(strip_tags($body), 0, 200);
                echo "Body Preview: " . trim($bodyPreview) . "...\n";
            }
            
            echo "\n";
        }
    }
} else {
    echo "No unread emails found\n";
}

// Also check recent emails (last 5)
echo "\n=== Recent Emails (Last 5) ===\n";
$totalEmails = imap_num_msg($connection);
$startEmail = max(1, $totalEmails - 4);

for ($i = $totalEmails; $i >= $startEmail; $i--) {
    echo "--- Email #$i ---\n";
    
    $header = imap_headerinfo($connection, $i);
    if ($header) {
        $from = $header->from[0] ?? null;
        $senderEmail = $from ? strtolower($from->mailbox . '@' . $from->host) : 'Unknown';
        $senderName = ($from && isset($from->personal)) ? imap_mime_header_decode($from->personal)[0]->text : $senderEmail;
        $subject = isset($header->subject) ? imap_mime_header_decode($header->subject)[0]->text : 'No Subject';
        
        echo "From: $senderName <$senderEmail>\n";
        echo "Subject: $subject\n";
        echo "Date: " . date('Y-m-d H:i:s', $header->udate) . "\n";
        
        // Check read status
        $flags = imap_fetch_overview($connection, $i);
        if ($flags && isset($flags[0])) {
            echo "Read: " . ($flags[0]->seen ? 'Yes' : 'No') . "\n";
        }
        
        echo "\n";
    }
}

imap_close($connection);

echo "=== Debug Complete ===\n";
?>
