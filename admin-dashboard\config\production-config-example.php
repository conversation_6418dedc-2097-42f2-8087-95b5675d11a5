<?php
/**
 * Production Database Configuration Example
 * 
 * IMPORTANT: This is an example file for production deployment.
 * Copy this file to config.php and update with your live server credentials.
 */

// Set default timezone to East Africa Time (UTC+3)
date_default_timezone_set('Africa/Nairobi');

class DatabaseConfig {
    // Database connection parameters - UPDATE THESE FOR PRODUCTION
    const DB_HOST = 'localhost'; // Usually 'localhost' on most hosting providers
    const DB_NAME = 'your_database_name'; // Update with your actual database name
    const DB_USER = 'your_database_user'; // Update with your database username
    const DB_PASS = 'your_database_password'; // Update with your database password
    const DB_CHARSET = 'utf8mb4';

    // Timezone configuration
    const TIMEZONE = 'Africa/Nairobi'; // East Africa Time (UTC+3)

    // Application settings
    const UPLOAD_PATH = 'uploads/images/';
    const MAX_FILE_SIZE = 5242880; // 5MB
    const ALLOWED_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

    // Session settings
    const SESSION_TIMEOUT = 1800; // 30 minutes for production (more secure)
    const MAX_LOGIN_ATTEMPTS = 5; // Maximum login attempts before lockout
    const LOCKOUT_DURATION = 900; // 15 minutes lockout duration
    const CSRF_TOKEN_LIFETIME = 3600; // 1 hour for CSRF tokens

    // Pagination
    const ITEMS_PER_PAGE = 10;
}

/**
 * PRODUCTION DEPLOYMENT CHECKLIST:
 * 
 * 1. Database Configuration:
 *    - Update DB_HOST, DB_NAME, DB_USER, DB_PASS with live server credentials
 *    - Ensure database exists and is properly configured
 *    - Import the database schema using database.sql
 *    - Create admin user account
 * 
 * 2. Email Configuration:
 *    - Verify email accounts (<EMAIL>, <EMAIL>) exist
 *    - Test SMTP connectivity from server
 *    - Ensure passwords are correct and accounts are not locked
 * 
 * 3. File Permissions:
 *    - Set proper permissions on uploads/ directory (755 or 775)
 *    - Ensure web server can write to uploads/images/
 *    - Set config files to 644 permissions
 * 
 * 4. Security:
 *    - Enable HTTPS/SSL certificate
 *    - Update .htaccess for production security
 *    - Change default admin password
 *    - Enable error logging but disable display_errors
 * 
 * 5. Testing:
 *    - Test admin login functionality
 *    - Test password reset email sending
 *    - Test image uploads
 *    - Test contact form submissions
 *    - Test quote form submissions
 * 
 * 6. Monitoring:
 *    - Set up error log monitoring
 *    - Monitor email delivery
 *    - Set up backup procedures
 */

/**
 * Environment Detection Helper
 */
function isProductionEnvironment() {
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    return !(strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false);
}

/**
 * Get Base URL for the application
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

    if (isProductionEnvironment()) {
        // Live server - no /meleva path needed
        return $protocol . '://' . $host;
    } else {
        // Localhost - include /meleva path
        return $protocol . '://' . $host . '/meleva';
    }
}

// Rest of the configuration remains the same as the original config.php
// (Database class, Auth class, Utils class, etc.)
