-- Migration: Add reviews table
-- Date: 2025-01-30
-- Description: Add reviews table for storing customer reviews (optional - currently using config file)

-- Check if reviews table exists, if not create it
CREATE TABLE IF NOT EXISTS reviews (
    review_id INT AUTO_INCREMENT PRIMARY KEY,
    customer_name VARCHAR(100) NOT NULL,
    customer_email VARCHAR(100),
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT NOT NULL,
    review_date VARCHAR(50),
    customer_location VARCHAR(100),
    source ENUM('google', 'manual', 'website', 'email') DEFAULT 'manual',
    is_featured BOOLEAN DEFAULT FALSE,
    is_approved BOOLEAN DEFAULT TRUE,
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by_user_id INT,
    <PERSON><PERSON><PERSON> created_by_user_id (created_by_user_id),
    <PERSON>E<PERSON> is_featured (is_featured),
    <PERSON><PERSON><PERSON> is_approved (is_approved),
    KEY display_order (display_order),
    FOREI<PERSON><PERSON> KEY (created_by_user_id) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Insert some sample reviews (optional)
INSERT IGNORE INTO reviews (customer_name, rating, review_text, review_date, customer_location, source, is_featured, display_order) VALUES
('<PERSON>', 5, 'Absolutely incredible safari experience! The guides were knowledgeable and the wildlife viewing was beyond our expectations. Meleva Tours made our dream trip come true.', 'December 2024', 'Nairobi, Kenya', 'google', TRUE, 1),
('Michael R.', 5, 'Professional service from start to finish. The Maasai Mara experience was breathtaking and the accommodation was top-notch. Highly recommend!', 'November 2024', 'Mombasa, Kenya', 'google', TRUE, 2),
('Emma Thompson', 5, 'Our family safari was perfectly organized. The kids loved every moment and we created memories that will last a lifetime. Thank you Meleva Tours!', 'October 2024', 'Kilifi, Kenya', 'google', TRUE, 3);
