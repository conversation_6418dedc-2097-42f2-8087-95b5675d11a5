<?php
session_start();
require_once '../includes/db.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_settings':
                updateReviewSettings($_POST);
                break;
            case 'update_reviews':
                updateManualReviews($_POST);
                break;
        }
    }
}

function updateReviewSettings($data) {
    $config_content = file_get_contents('../config/google-config.php');
    
    // Update settings
    $use_manual = isset($data['use_manual_reviews']) ? 'true' : 'false';
    $use_mixed = isset($data['use_mixed_reviews']) ? 'true' : 'false';
    
    $config_content = preg_replace(
        "/define\('USE_MANUAL_REVIEWS', (true|false)\);/",
        "define('USE_MANUAL_REVIEWS', $use_manual);",
        $config_content
    );
    
    $config_content = preg_replace(
        "/define\('USE_MIXED_REVIEWS', (true|false)\);/",
        "define('USE_MIXED_REVIEWS', $use_mixed);",
        $config_content
    );
    
    file_put_contents('../config/google-config.php', $config_content);
    echo "<div class='alert alert-success'>Settings updated successfully!</div>";
}

function updateManualReviews($data) {
    $reviews = [];
    
    for ($i = 0; $i < 5; $i++) {
        if (!empty($data["review_name_$i"])) {
            $reviews[] = [
                'name' => $data["review_name_$i"],
                'rating' => (int)$data["review_rating_$i"],
                'text' => $data["review_text_$i"],
                'date' => $data["review_date_$i"],
                'avatar' => null,
                'source' => 'Google',
                'location' => $data["review_location_$i"] ?? ''
            ];
        }
    }
    
    // Update config file
    $config_content = file_get_contents('../config/google-config.php');
    $reviews_php = var_export($reviews, true);
    
    $pattern = '/\$manual_reviews = \[.*?\];/s';
    $replacement = '$manual_reviews = ' . $reviews_php . ';';
    
    $config_content = preg_replace($pattern, $replacement, $config_content);
    file_put_contents('../config/google-config.php', $config_content);
    
    echo "<div class='alert alert-success'>Reviews updated successfully!</div>";
}

// Load current settings
require_once '../config/google-config.php';
$current_manual_reviews = isset($manual_reviews) ? $manual_reviews : [];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Reviews - Meleva Tours Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <h1 class="text-2xl font-bold text-gray-900">Manage Reviews</h1>
                    <a href="dashboard.php" class="text-orange-500 hover:text-orange-600">← Back to Dashboard</a>
                </div>
            </div>
        </header>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            
            <!-- Review Mode Settings -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4">Review Display Settings</h2>
                
                <form method="POST" class="space-y-4">
                    <input type="hidden" name="action" value="update_settings">
                    
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="radio" name="review_mode" value="manual" 
                                   <?php echo (defined('USE_MANUAL_REVIEWS') && USE_MANUAL_REVIEWS) ? 'checked' : ''; ?>
                                   class="mr-3">
                            <div>
                                <span class="font-medium">Manual Reviews Only</span>
                                <p class="text-sm text-gray-600">Display only the reviews you manually select below</p>
                            </div>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="radio" name="review_mode" value="mixed"
                                   <?php echo (defined('USE_MIXED_REVIEWS') && USE_MIXED_REVIEWS) ? 'checked' : ''; ?>
                                   class="mr-3">
                            <div>
                                <span class="font-medium">Mixed Reviews</span>
                                <p class="text-sm text-gray-600">Mix manual reviews with Google API reviews</p>
                            </div>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="radio" name="review_mode" value="google"
                                   <?php echo (!defined('USE_MANUAL_REVIEWS') || !USE_MANUAL_REVIEWS) && (!defined('USE_MIXED_REVIEWS') || !USE_MIXED_REVIEWS) ? 'checked' : ''; ?>
                                   class="mr-3">
                            <div>
                                <span class="font-medium">Google Reviews Only</span>
                                <p class="text-sm text-gray-600">Automatically fetch and display Google Business Profile reviews</p>
                            </div>
                        </label>
                    </div>
                    
                    <button type="submit" class="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600">
                        Update Settings
                    </button>
                </form>
            </div>

            <!-- Manual Reviews Editor -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">Manual Reviews</h2>
                <p class="text-gray-600 mb-6">Edit the reviews that will be displayed when manual mode is selected.</p>
                
                <form method="POST">
                    <input type="hidden" name="action" value="update_reviews">
                    
                    <?php for ($i = 0; $i < 5; $i++): 
                        $review = isset($current_manual_reviews[$i]) ? $current_manual_reviews[$i] : null;
                    ?>
                    <div class="border rounded-lg p-4 mb-4 <?php echo $review ? 'border-gray-300' : 'border-gray-200 bg-gray-50'; ?>">
                        <h3 class="font-medium mb-3">Review <?php echo $i + 1; ?></h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Customer Name</label>
                                <input type="text" name="review_name_<?php echo $i; ?>" 
                                       value="<?php echo $review ? htmlspecialchars($review['name']) : ''; ?>"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2"
                                       placeholder="e.g., Sarah Johnson">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Rating</label>
                                <select name="review_rating_<?php echo $i; ?>" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="5" <?php echo ($review && $review['rating'] == 5) ? 'selected' : ''; ?>>5 Stars</option>
                                    <option value="4" <?php echo ($review && $review['rating'] == 4) ? 'selected' : ''; ?>>4 Stars</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                                <input type="text" name="review_date_<?php echo $i; ?>" 
                                       value="<?php echo $review ? htmlspecialchars($review['date']) : ''; ?>"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2"
                                       placeholder="e.g., December 2024">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                                <input type="text" name="review_location_<?php echo $i; ?>" 
                                       value="<?php echo $review ? htmlspecialchars($review['location'] ?? '') : ''; ?>"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2"
                                       placeholder="e.g., Nairobi, Kenya">
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Review Text</label>
                            <textarea name="review_text_<?php echo $i; ?>" rows="3" 
                                      class="w-full border border-gray-300 rounded-md px-3 py-2"
                                      placeholder="Enter the customer's review..."><?php echo $review ? htmlspecialchars($review['text']) : ''; ?></textarea>
                        </div>
                    </div>
                    <?php endfor; ?>
                    
                    <button type="submit" class="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600">
                        Save Reviews
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Handle radio button changes
        document.querySelectorAll('input[name="review_mode"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const form = this.closest('form');
                const hiddenInputs = form.querySelectorAll('input[type="hidden"]');
                
                // Remove existing hidden inputs for checkboxes
                hiddenInputs.forEach(input => {
                    if (input.name === 'use_manual_reviews' || input.name === 'use_mixed_reviews') {
                        input.remove();
                    }
                });
                
                // Add appropriate hidden inputs based on selection
                if (this.value === 'manual') {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'use_manual_reviews';
                    input.value = '1';
                    form.appendChild(input);
                } else if (this.value === 'mixed') {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'use_mixed_reviews';
                    input.value = '1';
                    form.appendChild(input);
                }
            });
        });
    </script>
</body>
</html>
