<?php
/**
 * Debug Email Content Script
 * Shows what emails are actually in the working IMAP account
 */

require_once 'config/config.php';

// Check if IMAP extension is loaded
if (!extension_loaded('imap')) {
    die("ERROR: PHP IMAP extension is not installed.\n");
}

echo "=== Email Content Debug ===\n";
echo "Checking <NAME_EMAIL>...\n\n";

// Connect to the working account
$connectionString = "{melevatours.co.ke:993/imap/ssl}INBOX";
$connection = imap_open($connectionString, '<EMAIL>', 'hi$Ch9=lYcap{7cA');

if (!$connection) {
    die("Failed to connect to IMAP server\n");
}

// Get all emails
$emails = imap_search($connection, 'ALL');

if (!$emails) {
    echo "No emails found\n";
    imap_close($connection);
    exit;
}

echo "Found " . count($emails) . " emails\n\n";

// Show details of recent emails
$recentEmails = array_slice($emails, -10); // Last 10 emails

foreach ($recentEmails as $emailNumber) {
    echo "--- Email #$emailNumber ---\n";
    
    // Get header
    $header = imap_headerinfo($connection, $emailNumber);
    
    if ($header) {
        // Sender info
        $from = $header->from[0] ?? null;
        if ($from) {
            $senderEmail = strtolower($from->mailbox . '@' . $from->host);
            $senderName = isset($from->personal) ? imap_mime_header_decode($from->personal)[0]->text : $senderEmail;
            echo "From: $senderName <$senderEmail>\n";
        }
        
        // Subject
        $subject = isset($header->subject) ? imap_mime_header_decode($header->subject)[0]->text : 'No Subject';
        echo "Subject: $subject\n";
        
        // Date
        echo "Date: " . date('Y-m-d H:i:s', $header->udate) . "\n";
        
        // Message ID
        echo "Message ID: " . ($header->message_id ?? 'None') . "\n";
        
        // In-Reply-To
        echo "In-Reply-To: " . ($header->in_reply_to ?? 'None') . "\n";
        
        // Read status
        $flags = imap_fetch_overview($connection, $emailNumber);
        if ($flags && isset($flags[0])) {
            echo "Read: " . ($flags[0]->seen ? 'Yes' : 'No') . "\n";
        }
        
        // Get body preview
        $body = imap_body($connection, $emailNumber);
        $bodyPreview = substr(strip_tags($body), 0, 200);
        echo "Body Preview: " . trim($bodyPreview) . "...\n";
        
        echo "\n";
    }
}

// Check for unread emails specifically
echo "=== Unread Emails ===\n";
$unreadEmails = imap_search($connection, 'UNSEEN');

if ($unreadEmails) {
    echo "Found " . count($unreadEmails) . " unread emails:\n";
    foreach ($unreadEmails as $emailNumber) {
        $header = imap_headerinfo($connection, $emailNumber);
        if ($header) {
            $from = $header->from[0] ?? null;
            $senderEmail = $from ? strtolower($from->mailbox . '@' . $from->host) : 'Unknown';
            $subject = isset($header->subject) ? imap_mime_header_decode($header->subject)[0]->text : 'No Subject';
            echo "  #$emailNumber: $subject (from $senderEmail)\n";
        }
    }
} else {
    echo "No unread emails found\n";
}

// Check what's in the database
echo "\n=== Database Messages ===\n";
try {
    $db = Database::getInstance()->getConnection();
    
    // Get recent messages from database
    $stmt = $db->prepare("SELECT * FROM messages ORDER BY received_at DESC LIMIT 10");
    $stmt->execute();
    $dbMessages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($dbMessages) {
        echo "Found " . count($dbMessages) . " messages in database:\n";
        foreach ($dbMessages as $msg) {
            echo "  ID: {$msg['message_id']} | From: {$msg['sender_name']} <{$msg['sender_email']}> | Subject: {$msg['subject']} | Date: {$msg['received_at']}\n";
        }
    } else {
        echo "No messages found in database\n";
    }
    
    // Check for any booking-related emails in database
    $stmt = $db->prepare("SELECT * FROM messages WHERE sender_email LIKE '%booking%' OR subject LIKE '%booking%' OR message_content LIKE '%booking%' ORDER BY received_at DESC LIMIT 5");
    $stmt->execute();
    $bookingMessages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($bookingMessages) {
        echo "\nBooking-related messages in database:\n";
        foreach ($bookingMessages as $msg) {
            echo "  From: {$msg['sender_name']} <{$msg['sender_email']}> | Subject: {$msg['subject']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}

imap_close($connection);

echo "\n=== Summary ===\n";
echo "1. <EMAIL> is working and has emails\n";
echo "2. <EMAIL> authentication fails\n";
echo "3. Check if emails <NAME_EMAIL> are being <NAME_EMAIL>\n";
echo "4. <NAME_EMAIL> account needs to be created/configured\n";
?>
