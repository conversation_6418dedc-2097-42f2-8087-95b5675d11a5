<?php
require_once 'config/config.php';

$db = Database::getInstance()->getConnection();

echo "=== Current messages table structure ===\n";
$stmt = $db->query("DESCRIBE messages");
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($columns as $column) {
    echo $column['Field'] . " | " . $column['Type'] . " | " . $column['Null'] . " | " . $column['Key'] . " | " . $column['Default'] . " | " . $column['Extra'] . "\n";
}

echo "\n=== Checking for message_type column ===\n";
$hasMessageType = false;
foreach ($columns as $column) {
    if ($column['Field'] === 'message_type') {
        $hasMessageType = true;
        echo "✅ message_type column exists: " . $column['Type'] . "\n";
        break;
    }
}

if (!$hasMessageType) {
    echo "❌ message_type column is missing\n";
    echo "Adding message_type column...\n";
    
    try {
        $db->exec("ALTER TABLE messages ADD COLUMN message_type ENUM('incoming', 'outgoing') DEFAULT 'incoming' AFTER email_thread_id");
        echo "✅ Successfully added message_type column\n";
    } catch (Exception $e) {
        echo "❌ Error adding column: " . $e->getMessage() . "\n";
    }
}
?>
