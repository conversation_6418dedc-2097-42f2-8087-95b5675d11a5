<?php
require_once 'config/config.php';

$db = Database::getInstance()->getConnection();

echo "=== Latest Messages (Last 15) ===\n";
$stmt = $db->prepare("SELECT * FROM messages ORDER BY received_at DESC LIMIT 15");
$stmt->execute();
$messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($messages as $msg) {
    $type = $msg['message_type'] ?? 'NULL';
    $read = $msg['is_read'] ? 'Yes' : 'No';
    echo "ID: {$msg['message_id']} | Type: $type | From: {$msg['sender_name']} | Email: {$msg['sender_email']} | Subject: {$msg['subject']} | Date: {$msg['received_at']} | Read: $read\n";
}

echo "\n=== Unread Messages Count ===\n";
$stmt = $db->prepare("SELECT COUNT(*) as count FROM messages WHERE is_read = 0");
$stmt->execute();
$unread = $stmt->fetch(PDO::FETCH_ASSOC);
echo "Total unread messages: {$unread['count']}\n";

echo "\n=== Messages from today ===\n";
$stmt = $db->prepare("SELECT * FROM messages WHERE DATE(received_at) = CURDATE() ORDER BY received_at DESC");
$stmt->execute();
$todayMessages = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($todayMessages) {
    foreach ($todayMessages as $msg) {
        $type = $msg['message_type'] ?? 'NULL';
        $read = $msg['is_read'] ? 'Yes' : 'No';
        echo "ID: {$msg['message_id']} | Type: $type | From: {$msg['sender_name']} | Subject: {$msg['subject']} | Date: {$msg['received_at']} | Read: $read\n";
    }
} else {
    echo "No messages from today\n";
}
?>
