<?php
/**
 * Test script to verify instant message updates functionality
 */

require_once 'config/config.php';

$db = Database::getInstance()->getConnection();

echo "=== Testing Instant Message Updates ===\n";

// Check if we have unread messages
$stmt = $db->query("SELECT COUNT(*) as count FROM messages WHERE is_read = 0");
$unreadCount = $stmt->fetch()['count'];
echo "Current unread messages: {$unreadCount}\n";

// Check if API endpoints exist
$apiFiles = [
    'api/get_message.php',
    'api/get_unread_count.php', 
    'api/update_message_status.php',
    'api/bulk_update_messages.php'
];

echo "\n=== API Endpoints Check ===\n";
foreach ($apiFiles as $file) {
    if (file_exists($file)) {
        echo "✅ {$file} exists\n";
    } else {
        echo "❌ {$file} missing\n";
    }
}

// Test the unread count API
echo "\n=== Testing Unread Count API ===\n";
if (file_exists('api/get_unread_count.php')) {
    // Simulate API call
    ob_start();
    $_SESSION['admin_logged_in'] = true; // Simulate admin login
    include 'api/get_unread_count.php';
    $output = ob_get_clean();
    
    $response = json_decode($output, true);
    if ($response && $response['success']) {
        echo "✅ API working - Unread count: {$response['count']}\n";
    } else {
        echo "❌ API error: " . ($response['error'] ?? 'Unknown error') . "\n";
    }
} else {
    echo "❌ API file not found\n";
}

// Show recent messages for testing
echo "\n=== Recent Messages for Testing ===\n";
$stmt = $db->prepare("
    SELECT message_id, sender_name, subject, is_read, message_category, received_at
    FROM messages 
    ORDER BY received_at DESC 
    LIMIT 5
");
$stmt->execute();
$messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($messages as $msg) {
    $status = $msg['is_read'] ? 'READ' : 'UNREAD';
    echo "ID {$msg['message_id']}: {$msg['sender_name']} - {$msg['subject']} ({$status}) [{$msg['message_category']}]\n";
}

echo "\n=== Test Complete ===\n";
echo "You can now test the instant updates by:\n";
echo "1. Opening http://localhost:8000/admin-dashboard/messages.php\n";
echo "2. Clicking on an unread message (should instantly remove bold styling)\n";
echo "3. Using Mark as Read/Unread buttons (should update instantly)\n";
echo "4. Using bulk actions (should update multiple messages instantly)\n";
?>
