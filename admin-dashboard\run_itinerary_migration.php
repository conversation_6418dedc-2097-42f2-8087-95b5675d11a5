<?php
/**
 * Migration Runner: Add Itinerary Field to Tour Packages
 * 
 * This script adds the itinerary field to the tour_packages table
 * for existing databases that don't have this field yet.
 */

// Define admin access constant
define('ADMIN_ACCESS', true);

// Include required files
require_once 'config/config.php';

// Only allow execution from command line or admin users
if (php_sapi_name() !== 'cli') {
    // Include security middleware for web access
    require_once 'includes/security_middleware.php';
    
    // Require admin role
    requireRole('admin');
    
    // Set content type for web output
    header('Content-Type: text/html; charset=UTF-8');
    echo "<!DOCTYPE html><html><head><title>Itinerary Migration</title></head><body>";
    echo "<h2>Running Migration: Add Itinerary Field</h2>";
    echo "<pre>";
}

try {
    $db = Database::getInstance()->getConnection();
    
    echo "Database connection successful\n";
    
    $migrationFile = __DIR__ . '/migrations/add_itinerary_field.sql';
    echo "Reading migration file: $migrationFile\n";

    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: $migrationFile");
    }

    $migration = file_get_contents($migrationFile);
    echo "Migration file size: " . strlen($migration) . " bytes\n";

    // Split the migration into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $migration)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );

    echo "Found " . count($statements) . " SQL statements to execute\n\n";

    // Execute each statement
    foreach ($statements as $index => $statement) {
        if (empty(trim($statement))) continue;
        
        echo "Executing statement " . ($index + 1) . "...\n";
        echo "SQL: " . substr($statement, 0, 100) . (strlen($statement) > 100 ? '...' : '') . "\n";
        
        try {
            $stmt = $db->prepare($statement);
            $stmt->execute();
            
            // If this is a SELECT statement, show results
            if (stripos(trim($statement), 'SELECT') === 0) {
                $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
                if (!empty($results)) {
                    echo "Results:\n";
                    foreach ($results as $row) {
                        foreach ($row as $key => $value) {
                            echo "  $key: $value\n";
                        }
                    }
                }
            }
            
            echo "✓ Statement executed successfully\n\n";
            
        } catch (PDOException $e) {
            echo "✗ Error executing statement: " . $e->getMessage() . "\n\n";
            // Continue with other statements
        }
    }

    echo "Migration completed successfully!\n";
    echo "The itinerary field has been added to the tour_packages table.\n";

} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}

if (php_sapi_name() !== 'cli') {
    echo "</pre>";
    echo "<p><a href='packages.php'>Go to Packages Management</a></p>";
    echo "</body></html>";
}
?>
