<?php
/**
 * IMAP Connection Test Script
 * Tests IMAP connectivity to debug email fetching issues
 */

// Check if IMAP extension is loaded
if (!extension_loaded('imap')) {
    die("ERROR: PHP IMAP extension is not installed.\n");
}

echo "=== IMAP Connection Test ===\n";
echo "Testing IMAP connectivity for Meleva Tours email accounts...\n\n";

// Test configurations
$testConfigs = [
    [
        'name' => '<EMAIL>',
        'email' => '<EMAIL>',
        'password' => 'hi$Ch9=lYcap{7cA',
        'host' => 'mail.melevatours.co.ke',
        'port' => 993,
        'flags' => '/imap/ssl'
    ],
    [
        'name' => '<EMAIL>',
        'email' => '<EMAIL>',
        'password' => '1V^bAvDR!%)6,C&A',
        'host' => 'mail.melevatours.co.ke',
        'port' => 993,
        'flags' => '/imap/ssl'
    ]
];

// Alternative configurations to try
$alternativeConfigs = [
    [
        'name' => 'Alternative 1 - mail.melevatours.co.ke',
        'host' => 'mail.melevatours.co.ke',
        'port' => 993,
        'flags' => '/imap/ssl'
    ],
    [
        'name' => 'Alternative 2 - No SSL',
        'host' => 'melevatours.co.ke',
        'port' => 143,
        'flags' => '/imap/notls'
    ],
    [
        'name' => 'Alternative 3 - mail.melevatours.co.ke No SSL',
        'host' => 'mail.melevatours.co.ke',
        'port' => 143,
        'flags' => '/imap/notls'
    ]
];

foreach ($testConfigs as $config) {
    echo "--- Testing {$config['name']} ---\n";
    
    // Build connection string
    $connectionString = "{{$config['host']}:{$config['port']}{$config['flags']}}INBOX";
    echo "Connection String: $connectionString\n";
    
    // Clear any previous IMAP errors
    imap_errors();
    imap_alerts();
    
    // Attempt connection
    $connection = @imap_open($connectionString, $config['email'], $config['password']);
    
    if ($connection) {
        echo "✅ SUCCESS: Connected successfully!\n";
        
        // Get mailbox info
        $mailboxInfo = imap_status($connection, $connectionString, SA_ALL);
        if ($mailboxInfo) {
            echo "   Messages: {$mailboxInfo->messages}\n";
            echo "   Unread: {$mailboxInfo->unseen}\n";
            echo "   Recent: {$mailboxInfo->recent}\n";
        }
        
        // Test searching for emails
        $emails = imap_search($connection, 'ALL');
        if ($emails) {
            echo "   Total emails found: " . count($emails) . "\n";
            
            // Get info about the most recent email
            if (count($emails) > 0) {
                $latestEmail = end($emails);
                $header = imap_headerinfo($connection, $latestEmail);
                if ($header) {
                    echo "   Latest email from: " . ($header->from[0]->personal ?? $header->from[0]->mailbox . '@' . $header->from[0]->host) . "\n";
                    echo "   Latest email subject: " . ($header->subject ?? 'No subject') . "\n";
                    echo "   Latest email date: " . date('Y-m-d H:i:s', $header->udate) . "\n";
                }
            }
        } else {
            echo "   No emails found or search failed\n";
        }
        
        imap_close($connection);
        echo "\n";
    } else {
        echo "❌ FAILED: Could not connect\n";
        
        // Get detailed error information
        $errors = imap_errors();
        $alerts = imap_alerts();
        
        if ($errors) {
            echo "   IMAP Errors:\n";
            foreach ($errors as $error) {
                echo "   - $error\n";
            }
        }
        
        if ($alerts) {
            echo "   IMAP Alerts:\n";
            foreach ($alerts as $alert) {
                echo "   - $alert\n";
            }
        }
        
        echo "\n   Trying alternative configurations...\n";
        
        // Try alternative configurations
        foreach ($alternativeConfigs as $altConfig) {
            echo "   Testing {$altConfig['name']}...\n";
            
            $altConnectionString = "{{$altConfig['host']}:{$altConfig['port']}{$altConfig['flags']}}INBOX";
            echo "   Connection String: $altConnectionString\n";
            
            // Clear errors
            imap_errors();
            imap_alerts();
            
            $altConnection = @imap_open($altConnectionString, $config['email'], $config['password']);
            
            if ($altConnection) {
                echo "   ✅ SUCCESS with alternative config!\n";
                echo "   Recommended settings:\n";
                echo "   - Host: {$altConfig['host']}\n";
                echo "   - Port: {$altConfig['port']}\n";
                echo "   - Flags: {$altConfig['flags']}\n";
                
                imap_close($altConnection);
                break;
            } else {
                echo "   ❌ Failed\n";
                $altErrors = imap_errors();
                if ($altErrors) {
                    foreach ($altErrors as $error) {
                        echo "     Error: $error\n";
                    }
                }
            }
        }
        echo "\n";
    }
}

echo "=== Test Complete ===\n";
echo "\nIf all tests failed, possible issues:\n";
echo "1. Email accounts don't exist or passwords are incorrect\n";
echo "2. IMAP is not enabled for these accounts\n";
echo "3. Server firewall is blocking IMAP connections\n";
echo "4. Email server requires different settings\n";
echo "\nNext steps:\n";
echo "1. Verify email accounts exist in webmail\n";
echo "2. Check if IMAP is enabled in email account settings\n";
echo "3. Contact hosting provider about IMAP access\n";
echo "4. Try accessing webmail to confirm credentials\n";
?>
