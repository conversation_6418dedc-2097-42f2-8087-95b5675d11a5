<?php
require_once 'config/config.php';

$db = Database::getInstance()->getConnection();

echo "=== Searching for <PERSON>'s Recent Emails ===\n";

// Search for <PERSON>'s emails from the last 2 days
$stmt = $db->prepare("
    SELECT * FROM messages 
    WHERE sender_email = '<EMAIL>' 
    AND received_at >= DATE_SUB(NOW(), INTERVAL 2 DAY)
    ORDER BY received_at DESC
");
$stmt->execute();
$emmanuelEmails = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($emmanuelEmails) {
    echo "Found " . count($emmanuelEmails) . " emails from <PERSON> in the last 2 days:\n\n";
    
    foreach ($emmanuelEmails as $email) {
        $type = $email['message_type'] ?? 'NULL';
        $read = $email['is_read'] ? 'Yes' : 'No';
        echo "ID: {$email['message_id']} | Type: $type | Subject: {$email['subject']} | Date: {$email['received_at']} | Read: $read\n";
        
        // Show a preview of the content
        $preview = substr(strip_tags($email['message_content']), 0, 100);
        echo "   Content: " . trim($preview) . "...\n\n";
    }
} else {
    echo "No emails found from Emmanuel in the last 2 days\n";
}

// Check for any emails with "Payment Receipt" in subject
echo "=== Searching for Payment Receipt Emails ===\n";
$stmt = $db->prepare("
    SELECT * FROM messages 
    WHERE subject LIKE '%Payment Receipt%' 
    AND received_at >= DATE_SUB(NOW(), INTERVAL 2 DAY)
    ORDER BY received_at DESC
");
$stmt->execute();
$paymentEmails = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($paymentEmails) {
    echo "Found " . count($paymentEmails) . " payment receipt related emails:\n\n";
    
    foreach ($paymentEmails as $email) {
        $type = $email['message_type'] ?? 'NULL';
        $read = $email['is_read'] ? 'Yes' : 'No';
        echo "ID: {$email['message_id']} | Type: $type | From: {$email['sender_name']} | Subject: {$email['subject']} | Date: {$email['received_at']} | Read: $read\n";
    }
} else {
    echo "No payment receipt emails found\n";
}

// Check the very latest messages in the database
echo "\n=== Latest 5 Messages in Database ===\n";
$stmt = $db->prepare("SELECT * FROM messages ORDER BY received_at DESC LIMIT 5");
$stmt->execute();
$latestMessages = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($latestMessages as $msg) {
    $type = $msg['message_type'] ?? 'NULL';
    $read = $msg['is_read'] ? 'Yes' : 'No';
    echo "ID: {$msg['message_id']} | Type: $type | From: {$msg['sender_name']} | Subject: {$msg['subject']} | Date: {$msg['received_at']} | Read: $read\n";
}
?>
