<?php
require_once 'config/config.php';

$db = Database::getInstance()->getConnection();

echo "=== Recent Customer Messages ===\n";
$stmt = $db->prepare("SELECT * FROM messages WHERE message_type = 'incoming' OR message_type IS NULL ORDER BY received_at DESC LIMIT 10");
$stmt->execute();
$messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($messages as $msg) {
    $type = $msg['message_type'] ?? 'NULL';
    $read = $msg['is_read'] ? 'Yes' : 'No';
    echo "ID: {$msg['message_id']} | Type: $type | From: {$msg['sender_name']} | Email: {$msg['sender_email']} | Subject: {$msg['subject']} | Date: {$msg['received_at']} | Read: $read\n";
}

echo "\n=== Unread Messages ===\n";
$stmt = $db->prepare("SELECT COUNT(*) as count FROM messages WHERE is_read = 0 AND (message_type = 'incoming' OR message_type IS NULL)");
$stmt->execute();
$unread = $stmt->fetch(PDO::FETCH_ASSOC);
echo "Unread messages: {$unread['count']}\n";

echo "\n=== All Message Types ===\n";
$stmt = $db->prepare("SELECT message_type, COUNT(*) as count FROM messages GROUP BY message_type");
$stmt->execute();
$types = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($types as $type) {
    $typeLabel = $type['message_type'] ?? 'NULL';
    echo "$typeLabel: {$type['count']} messages\n";
}
?>
